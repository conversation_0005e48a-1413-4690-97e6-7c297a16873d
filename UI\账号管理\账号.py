from PyQt5.QtWidgets import (QDialog, QFormLayout, QLineEdit, 
                            QMessageBox, QDialogButtonBox)
from PyQt5.QtCore import Qt
import sqlite3
import os

class AccountDialog(QDialog):
    def __init__(self, title, parent=None, data=None):
        super().__init__(parent)
        self.account_data = {}
        self.setWindowTitle(title)
        self.setStyleSheet("""
            QDialog {
                background-color: rgba(20, 25, 45, 0.9);
                border: 1px solid rgba(0, 245, 255, 0.3);
                border-top: 3px solid qlineargradient(
                    x1:0, y1:0, x2:0, y2:1,
                    stop:0 #00F5FF, stop:1 transparent
                );
            }
            QLabel {
                color: #FFFFFF;
                font-family: "Segoe UI";
                font-size: 10pt;
                font-weight: bold;
                background-color: rgba(20, 25, 45, 0.7);
                padding: 2px 5px;
                border-radius: 3px;
            }
            QLineEdit {
                background-color: rgba(37, 117, 252, 0.3);
                color: #C0C0C0;
                border: 1px solid rgba(0, 245, 255, 0.3);
                border-radius: 4px;
                padding: 5px;
                font-family: "Segoe UI";
                font-size: 10pt;
            }
            QDialogButtonBox {
                background-color: transparent;
            }
            QPushButton {
                background-color: rgba(37, 117, 252, 0.3);
                color: #00F5FF;
                border: 1px solid rgba(0, 245, 255, 0.3);
                border-radius: 4px;
                padding: 5px 15px;
                min-width: 80px;
                font-family: "Segoe UI";
            }
            QPushButton:hover {
                background-color: rgba(37, 117, 252, 0.5);
                color: white;
            }
        """)
        
        layout = QFormLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)
        
        self.username = QLineEdit()
        self.password = QLineEdit()
        self.device_id = QLineEdit()
        self.login_status = QLineEdit()
        self.login_status.setReadOnly(True)
        self.login_server = QLineEdit()
        self.login_server.setReadOnly(True)
        self.real_name = QLineEdit()
        self.phone = QLineEdit()
        self.id_card = QLineEdit()
        self.ban_status = QLineEdit()
        self.remark = QLineEdit()
        
        if data:
            if isinstance(data, (tuple, list)) and len(data) >= 10:
                self.username.setText(str(data[1] if data[1] is not None else ""))
                self.password.setText(str(data[2] if data[2] is not None else ""))
                self.device_id.setText(str(data[3] if data[3] is not None else ""))
                self.login_status.setText(str(data[4] if data[4] is not None else ""))
                self.real_name.setText(str(data[5] if data[5] is not None else ""))
                self.phone.setText(str(data[6] if data[6] is not None else ""))
                self.id_card.setText(str(data[7] if data[7] is not None else ""))
                self.ban_status.setText(str(data[8] if data[8] is not None else ""))
                self.remark.setText(str(data[9] if data[9] is not None else ""))
        
        layout.addRow("账号:", self.username)
        layout.addRow("密码:", self.password)
        layout.addRow("手机ID:", self.device_id)
        layout.addRow("登录状态:", self.login_status)
        layout.addRow("登录区服:", self.login_server)
        layout.addRow("注册姓名:", self.real_name)
        layout.addRow("手机:", self.phone)
        layout.addRow("身份证:", self.id_card)
        layout.addRow("封号状态:", self.ban_status)
        layout.addRow("备注:", self.remark)
        
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        
        layout.addRow(buttons)
        self.setFixedSize(350, 450)

    def accept(self):
        if not self.username.text().strip():
            self.show_error("账号不能为空")
            return
            
        if not self.password.text().strip():
            self.show_error("密码不能为空")
            return
            
        self.account_data = {
            'username': self.username.text(),
            'password': self.password.text(),
            'device_id': self.device_id.text(),
            'login_status': self.login_status.text(),
            'login_server': self.login_server.text(),
            'real_name': self.real_name.text(),
            'phone': self.phone.text(),
            'id_card': self.id_card.text(),
            'ban_status': self.ban_status.text(),
            'remark': self.remark.text()
        }
        super().accept()

    def show_error(self, message):
        msg = QMessageBox(self)
        msg.setWindowTitle("错误")
        msg.setText(message)
        msg.setIcon(QMessageBox.Warning)
        msg.setStandardButtons(QMessageBox.Ok)
        msg.setAttribute(Qt.WA_DeleteOnClose)
        msg.setStyleSheet("""
            QMessageBox {
                background-color: rgba(20, 25, 45, 0.95);
                border: 2px solid #FF5555;
                border-top: 3px solid qlineargradient(
                    x1:0, y1:0, x2:0, y2:1,
                    stop:0 #FF5555, stop:1 transparent
                );
            }
            QLabel {
                color: #FFFFFF;
                font-size: 14px;
                margin-top: 5px;
                font-weight: bold;
            }
            QPushButton {
                color: #FFFFFF;
                background-color: rgba(255, 85, 85, 0.5);
                border: 1px solid #FF5555;
                border-radius: 4px;
                padding: 5px 15px;
                min-width: 80px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: rgba(255, 85, 85, 0.7);
                border: 1px solid white;
            }
        """)
        msg.exec_()

    def get_account_data(self):
        return self.account_data


class AccountManager:
    def __init__(self, db_path):
        self.db_path = db_path
        self.init_db()

    def init_db(self):
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 检查表是否存在
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='账号表'")
            table_exists = cursor.fetchone()
            
            if table_exists:
                # 表已存在，检查列顺序是否正确
                cursor.execute("PRAGMA table_info(账号表)")
                columns = [column[1] for column in cursor.fetchall()]
                
                # 如果列顺序不正确，重建表
                if len(columns) > 0 and columns.index('登录区服') != columns.index('登录状态') + 1:
                    # 创建临时表保存数据
                    cursor.execute('''
                        CREATE TABLE IF NOT EXISTS 账号表_temp (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            用户名 TEXT NOT NULL UNIQUE,
                            密码 TEXT NOT NULL,
                            设备ID TEXT UNIQUE,
                            登录状态 TEXT DEFAULT '未登录',
                            登录区服 TEXT,
                            真实姓名 TEXT,
                            手机号 TEXT CHECK(length(手机号) <= 20),
                            身份证号 TEXT CHECK(length(身份证号) <= 18),
                            封禁状态 TEXT DEFAULT '正常',
                            备注 TEXT,
                            CHECK(登录状态 IN ('未登录', '登录中', '已登录')),
                            CHECK(封禁状态 IN ('正常', '已封号', '临时封禁'))
                        )
                    ''')
                    
                    # 迁移数据并处理无效的状态值
                    cursor.execute('''
                        INSERT INTO 账号表_temp 
                        SELECT id, 用户名, 密码, 设备ID, 
                               CASE WHEN 登录状态 IN ('未登录', '登录中', '已登录') 
                                    THEN 登录状态 ELSE '未登录' END,
                               登录区服, 真实姓名, 手机号, 身份证号,
                               CASE WHEN 封禁状态 IN ('正常', '已封号', '临时封禁') 
                                    THEN 封禁状态 ELSE '正常' END,
                               备注
                        FROM 账号表
                    ''')
                    
                    # 删除旧表并重命名
                    cursor.execute("DROP TABLE 账号表")
                    cursor.execute("ALTER TABLE 账号表_temp RENAME TO 账号表")
                elif '登录区服' not in columns:
                    cursor.execute("ALTER TABLE 账号表 ADD COLUMN 登录区服 TEXT")
            
            # 创建或更新表结构
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS 账号表 (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    用户名 TEXT NOT NULL UNIQUE,
                    密码 TEXT NOT NULL,
                    设备ID TEXT UNIQUE,
                    登录状态 TEXT DEFAULT '未登录',
                    登录区服 TEXT,
                    真实姓名 TEXT,
                    手机号 TEXT CHECK(length(手机号) <= 20),
                    身份证号 TEXT CHECK(length(身份证号) <= 18),
                    封禁状态 TEXT DEFAULT '正常',
                    备注 TEXT,
                    CHECK(登录状态 IN ('未登录', '登录中', '已登录')),
                    CHECK(封禁状态 IN ('正常', '已封号', '临时封禁'))
                )
            ''')
            
            # 创建索引
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_用户名 ON 账号表(用户名)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_设备ID ON 账号表(设备ID)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_登录状态 ON 账号表(登录状态)')
            
            conn.commit()
            conn.close()
        except Exception as e:
            raise Exception(f"初始化数据库失败: {str(e)}")

    def add_account(self, account_data):
        try:
            # 验证登录状态值
            valid_status = ['未登录', '登录中', '已登录']
            login_status = account_data.get('login_status', '未登录')
            if login_status not in valid_status:
                login_status = '未登录'
                
            # 验证封禁状态值
            valid_ban_status = ['正常', '已封号', '临时封禁']
            ban_status = account_data.get('ban_status', '正常')
            if ban_status not in valid_ban_status:
                ban_status = '正常'
                
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO 账号表 (用户名, 密码, 设备ID, 登录状态, 登录区服,
                                    真实姓名, 手机号, 身份证号, 封禁状态, 备注)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                account_data['username'],
                account_data['password'],
                account_data['device_id'],
                login_status,
                account_data['login_server'],
                account_data['real_name'],
                account_data['phone'],
                account_data['id_card'],
                ban_status,
                account_data['remark']
            ))
            account_id = cursor.lastrowid
            
            # 自动添加区服标签对应的区服记录
            cursor.execute("SELECT 标签名称 FROM 区服标签")
            tags = cursor.fetchall()
            for tag in tags:
                cursor.execute('''
                    INSERT INTO 区服表 (账号ID, 区服名称, 完成状态)
                    VALUES (?, ?, ?)
                ''', (account_id, tag[0], "未完成"))
            
            conn.commit()
            conn.close()
            return True
        except Exception as e:
            raise Exception(f"添加账号失败: {str(e)}")

    def update_account(self, account_id, account_data):
        try:
            # 获取当前账号的登录状态和区服
            current_account = self.get_account_by_username(account_id)
            if not current_account:
                raise Exception("账号不存在")
                
            # 验证封禁状态值
            valid_ban_status = ['正常', '已封号', '临时封禁']
            ban_status = account_data.get('ban_status', '正常')
            if ban_status not in valid_ban_status:
                ban_status = '正常'
                
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute('''
                UPDATE 账号表 SET
                    用户名=?, 密码=?, 设备ID=?,
                    真实姓名=?, 手机号=?, 身份证号=?, 封禁状态=?, 备注=?
                WHERE 用户名=?
            ''', (
                account_data['username'],
                account_data['password'],
                account_data['device_id'],
                account_data['real_name'],
                account_data['phone'],
                account_data['id_card'],
                ban_status,
                account_data['remark'],
                account_id
            ))
            conn.commit()
            conn.close()
            return True
        except Exception as e:
            raise Exception(f"更新账号失败: {str(e)}")

    def delete_account(self, username):
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("DELETE FROM 账号表 WHERE 用户名=?", (username,))
            conn.commit()
            conn.close()
            return True
        except Exception as e:
            raise Exception(f"删除账号失败: {str(e)}")

    def get_all_accounts(self):
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM 账号表")
            rows = cursor.fetchall()
            conn.close()
            return rows
        except Exception as e:
            raise Exception(f"获取账号列表失败: {str(e)}")

    def account_exists(self, username):
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT id FROM 账号表 WHERE 用户名=?", (username,))
            exists = cursor.fetchone() is not None
            conn.close()
            return exists
        except Exception as e:
            raise Exception(f"检查账号存在失败: {str(e)}")

    def get_account_by_username(self, username):
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM 账号表 WHERE 用户名=?", (username,))
            account = cursor.fetchone()
            conn.close()
            return account
        except Exception as e:
            raise Exception(f"获取账号信息失败: {str(e)}")

    def get_account_id(self, username):
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT id FROM 账号表 WHERE 用户名=?", (username,))
            account_id = cursor.fetchone()[0]
            conn.close()
            return account_id
        except Exception as e:
            raise Exception(f"获取账号ID失败: {str(e)}")

    def get_login_status(self, username):
        """获取指定账号的登录状态"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT 登录状态 FROM 账号表 WHERE 用户名=?", (username,))
            result = cursor.fetchone()
            conn.close()
            if result:
                return result[0]
            return None
        except Exception as e:
            raise Exception(f"查询登录状态失败: {str(e)}")

    def update_login_status(self, username, status):
        """更新指定账号的登录状态"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute(
                "UPDATE 账号表 SET 登录状态=? WHERE 用户名=?",
                (status, username)
            )
            conn.commit()
            conn.close()
            return True
        except Exception as e:
            raise Exception(f"更新登录状态失败: {str(e)}")

    def reset_all_login_status(self, status="未登录"):
        """重置所有账号的登录状态"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute(
                "UPDATE 账号表 SET 登录状态=?",
                (status,)
            )
            conn.commit()
            conn.close()
            return True
        except Exception as e:
            raise Exception(f"重置登录状态失败: {str(e)}")

    def device_id_exists(self, device_id):
        """检查设备ID是否已存在"""
        try:
            if not device_id:  # 空设备ID视为不存在
                return False
                
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT id FROM 账号表 WHERE 设备ID=?", (device_id,))
            exists = cursor.fetchone() is not None
            conn.close()
            return exists
        except Exception as e:
            raise Exception(f"检查设备ID存在失败: {str(e)}")
