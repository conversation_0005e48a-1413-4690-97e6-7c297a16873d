from window_func import 连接管理,日志
from PyQt5.QtGui import QColor
import time
import random
from typing import Tuple, Optional
class Android:
    # 获取手机连接信息
    @classmethod
    def get_Connection_information(cls, obj):
        日志.log_message(f"获取手机连接信息", "信息")
        # 获取 Android 设备 ID 并保存到实例变量
        obj.android_id = obj.get_android_id()
        # 获取设备IP地址    
        ip = obj.get_device_ip()
        # 获取投屏组号
        group_id = obj.get_group_id() 
        # 获取投屏编号 
        identifier = obj.get_identifier()
        # 获取手机分辨率并转为整数元组(确保永远是横屏分辨率)
        size_dict = obj.get_window_size()
        if size_dict['height'] > size_dict['width']:  # 竖屏状态
            obj.resolution_b = (int(size_dict['height']), int(size_dict['width']))  # 反转宽高
        else:
            obj.resolution_b = (int(size_dict['width']), int(size_dict['height']))
        
        # 构建设备信息并更新表格
        device_info = {
            "device_id": obj.android_id,
            "ip": ip,
            "group": group_id,
            "identifier": identifier,
            "status": "已连接"
        }
        
        # # 添加日志记录
        日志.log_message(f"{obj.android_id} 手机连接加入, 信息")
        # 调用window_func添加表格行
        连接管理.add_connection_row(
            phone_id=obj.android_id,
            phone_ip=ip,
            group=group_id,
            number=identifier,
            status="已连接"
        )
        # # 设置超时重新连接
        # obj.set_android_timeout(30)
        # # # 初始化OCR识别
        # if_ocr = obj.init_ocr_server("127.0.0.1", True, False, False) 
        # # if_ocr = obj.init_ocr_server("*************", True, True, True) 
        # if if_ocr:
        #     日志.log_message(f"{obj.android_id} OCR初始化成功", "信息")
        #     连接管理.update_device_action_by_id(obj.android_id, "OCR成功")
        # else:
        #     日志.log_message(f"{obj.android_id} OCR初始化失败", "错误")
        #     连接管理.update_device_action_by_id(obj.android_id, "OCR失败")
        # #判断APP是否运行
        # if_app = obj.app_is_running("剑网3无界")
        # if if_app:
        #     日志.log_message(f"{obj.android_id} APP已运行", "信息")
        #     连接管理.update_device_action_by_id(obj.android_id, "APP已运行")
        # else:
        #     # 启动APP
        #     result = obj.start_app("剑网3无界", 5, 0.5)#("剑网3无界")
        #     连接管理.update_device_action_by_id(obj.android_id, "启动游戏")
        #     if result:
        #         连接管理.update_device_action_by_id(obj.android_id, "启动成功")
        #         日志.log_message(f"{obj.android_id} 启动APP成功", "信息")
        #     else:
        #         日志.log_message(f"{obj.android_id} 启动APP失败", "错误")
        #         连接管理.update_device_action_by_id(obj.android_id, "启动失败")
        # return 
    
    @classmethod
    def check_device_mode(cls, obj):
        """判断设备操作模式(无障碍/HID/异常)
        :param obj: AndroidBotMain实例
        :return: True(无障碍)/False(HID)/None(异常)
        """
        日志.log_message(f"{obj.android_id} 开始检测操作模式", "信息")
        result = obj.click((0, 0))  # 通过无障碍点击坐标判断  
        if result:
            连接管理.update_device_mode_by_id(obj.android_id, "无障碍")
            日志.log_message(f"{obj.android_id} 设备模式: 无障碍", "信息")
            return "无障碍"
        elif result == False:
            # 初始化hid
            结果 = obj.init_hid(obj.WinDriving)
            if 结果:
                日志.log_message(f"{obj.android_id} HID初始化成功", "信息")
                连接管理.update_device_mode_by_id(obj.android_id, "HID")
                return "HID"
            else:
                日志.log_message(f"{obj.android_id} HID初始化失败", "错误")
                连接管理.update_device_mode_by_id(obj.android_id, "HID失败")
                return False
        else:
            obj.device_mode = "异常"
            连接管理.update_device_mode_by_id(obj.android_id, "异常")
            日志.log_message(f"{obj.android_id} 设备模式检测异常", "警告")
            return False
        
    def coordinate_transform(self, coordinate, resolution_a, resolution_b):
        """分辨率坐标转换"""
        # 计算宽高比例
        width_ratio = resolution_b[0] / resolution_a[0]
        height_ratio = resolution_b[1] / resolution_a[1]
        
        # 处理单点坐标或矩形区域
        if len(coordinate) == 2:  # (x,y)
            return (int(coordinate[0] * width_ratio), 
                   int(coordinate[1] * height_ratio))
        elif len(coordinate) == 4:  # (x1,y1,x2,y2)
            return (int(coordinate[0] * width_ratio),
                    int(coordinate[1] * height_ratio),
                    int(coordinate[2] * width_ratio),
                    int(coordinate[3] * height_ratio))
        return coordinate

    @classmethod
    def random_offset(cls, coord, radius=0):
        """添加正态分布随机偏移"""
        x, y = coord
        offset_x = random.gauss(0, radius/3)  # 标准差为半径1/3
        offset_y = random.gauss(0, radius/3)
        # 限制最大偏移范围
        offset_x = max(-radius, min(radius, offset_x))
        offset_y = max(-radius, min(radius, offset_y))
        return (int(x + offset_x), int(y + offset_y))
#======================================OCR识别=====================================================================
    @classmethod
    def find_text_click(cls, obj, text, region=(0,0,0,0), algorithm=(0,0,0), scale=1, target_coord=None, timeout=0):
        """查找文本并点击（自动判断操作模式，带随机偏移）
        :param obj: AndroidBotMain实例
        :param text: 要查找的文本
        :param region: 查找区域 (x1,y1,x2,y2)，默认全屏
        :param algorithm: 二值化
        :param scale: 图片缩放比例，默认1
        :param target_coord: 目标坐标(x,y)，如果提供则优先使用此坐标
        :param timeout: 超时时间(秒)，0表示只查找一次
        :return: True(找到并点击成功)/False(未找到或点击失败)
        """
        start_time = time.time()
        # 分辨率转换处理
        if hasattr(obj, 'resolution_b') and isinstance(obj.resolution_b, tuple) and len(obj.resolution_b) == 2:
            if region != (0,0,0,0):
                region = obj.coordinate_transform(region, obj.resolution_a, obj.resolution_b)
                region = tuple(int(x) for x in region)  # 确保region为整数
            if target_coord:
                target_coord = obj.coordinate_transform(target_coord, obj.resolution_a, obj.resolution_b)
                target_coord = tuple(int(x) for x in target_coord)
        while True:
            坐标 = obj.find_text(text, region, algorithm, scale)
            if 坐标:
                if target_coord:  # 使用提供的目标坐标
                    final_coord = target_coord
                    日志.log_message(f"[{obj.android_id}] 使用目标坐标点击:{text} {final_coord}", "成功")
                else:  # 使用查找返回的坐标
                    # 修正坐标：乘以 scale（抵消 find_text 中的 / scale）
                    final_coord = (坐标[0] * scale, 坐标[1] * scale)
                    日志.log_message(f"[{obj.android_id}] 使用查找坐标点击:{text} {final_coord}", "成功")
                if obj.device_mode == "HID":
                    obj.hid_click(cls.random_offset(final_coord))
                elif obj.device_mode == "无障碍":
                    obj.click(cls.random_offset(final_coord))
                else:   
                    日志.log_message(f"{obj.android_id} 设备模式异常，无法点击", "错误")
                return True
            #时间判断
            current_time = time.time()
            elapsed = current_time - start_time
            if timeout <= 0 or elapsed >= timeout:
                # print("find_text_click 跳出循环")
                return False
    @classmethod
    def 找字_匹配_单击(
        cls,
        obj,
        text: str,
        region: Tuple[int, int, int, int] = (0, 0, 0, 0),
        algorithm: Tuple[int, int, int] = (0, 0, 0),
        scale: float = 1.0,
        target_coord: Optional[Tuple[int, int]] = None,
        timeout: int = 0,
        match_mode: str = "any_char"
    ) -> bool:
        """
        在屏幕上查找指定文本并执行点击操作（支持三种匹配模式）

        Args:
            obj (AndroidBotMain): AndroidBotMain实例对象
            text (str): 要查找的目标文本
            region (Tuple[int, int, int, int]): 搜索区域坐标(x1,y1,x2,y2)，默认全屏
            algorithm (Tuple[int, int, int]): 图像处理算法参数，格式为(算法类型, 阈值, 最大值)
            scale (float): 图像缩放比例，用于高DPI屏幕适配
            target_coord (Optional[Tuple[int, int]]): 强制指定点击坐标(x,y)，优先级高于自动计算坐标
            timeout (int): 超时时间（秒），0表示立即返回
            match_mode (str): 文本匹配模式，可选值：
                - "substring": 子串匹配（默认）
                - "exact": 完全匹配
                - "any_char": 任意字符匹配

        Returns:
            bool: 找到文本并点击成功返回True，否则返回False

        Raises:
            ValueError: 当传入无效的match_mode参数时抛出

        Examples:
            >>> # 模糊匹配按钮
            >>> 找字_匹配_单击(bot, "确定", substring)
            
            >>> # 精确匹配特殊符号
            >>> 找字_匹配_单击(bot, "✔", match_mode="exact")
            
            >>> # 任意字符匹配
            >>> 找字_匹配_单击(bot, "OK", match_mode="any_char")
        """
        start_time = time.time()

        # 分辨率转换处理
        if hasattr(obj, 'resolution_b') and isinstance(obj.resolution_b, tuple):
            if region != (0, 0, 0, 0):
                region = obj.coordinate_transform(region, obj.resolution_a, obj.resolution_b)
                region = tuple(int(x) for x in region)
            if target_coord:
                target_coord = obj.coordinate_transform(target_coord, obj.resolution_a, obj.resolution_b)
                target_coord = tuple(int(x) for x in target_coord)

        while True:
            ocr_results = obj._parsing_ocr_data(region, algorithm, scale)
            # print(f"找字_匹配_单击: {ocr_results}")
            if ocr_results:
                for item in ocr_results:
                    if len(item) >= 2 and isinstance(item[1], list):
                        recognized_text = item[1][0]
                        
                        # 匹配逻辑选择器
                        if match_mode == "exact":
                            is_matched = (text == recognized_text)
                        elif match_mode == "any_char":
                            is_matched = any(char in recognized_text for char in text)
                        elif match_mode == "substring":
                            is_matched = (text in recognized_text)
                        else:
                            raise ValueError(f"无效的match_mode参数: {match_mode}")
                        if is_matched:
                            # 获取OCR返回的四边形坐标点
                            [[left_x, left_top_y], [right_x, right_top_y], _, _] = item[0]
                            
                            # 计算字符中心坐标（不考虑region偏移）
                            char_center_x = (left_x + right_x) / 2
                            char_center_y = (left_top_y + right_top_y) / 2
                            
                            # 应用scale缩放（不除以scale）
                            base_x = int(char_center_x * scale)
                            base_y = int(char_center_y * scale)
                            
                            # 如果指定了region，需要加上region的偏移量
                            if region != (0, 0, 0, 0):
                                base_x += int(region[0] * scale)
                                base_y += int(region[1] * scale)
                            
                            # 计算最终坐标（考虑target_coord覆盖）
                            final_coord = target_coord if target_coord else (base_x, base_y)
                            
                            # 执行点击
                            if obj.device_mode == "HID":
                                obj.hid_click(cls.random_offset(final_coord))
                                日志.log_message(f"{obj.android_id} HID点击  {final_coord}", "调试")
                            elif obj.device_mode == "无障碍":
                                obj.click(cls.random_offset(final_coord))
                                日志.log_message(f"{obj.android_id} 无障碍点击  {final_coord}", "调试")
                            else:
                                print(f"{obj.android_id} 设备模式异常")
                                return False
                            
                            print(f"[{obj.android_id}] 匹配模式={match_mode} | 目标:'{text}' | 匹配:'{recognized_text}' | 坐标:{final_coord}")
                            return True
            #循环时间判断
            current_time = time.time()
            elapsed = current_time - start_time
            if timeout <= 0 or elapsed >= timeout:
                # print("找字_匹配_单击 跳出循环")
                return False
#================================================找色=====================================================
    @classmethod
    def 找色_单击(
        cls,
        obj,
        color: str,
        sub_colors: list = [],
        region: tuple = (0, 0, 0, 0),
        similarity: float = 0.9,
        wait_time: float = 0.01,
        interval_time: float = 0,
        target_coord: Optional[Tuple[int, int]] = None
    ) -> bool:
        """
        查找指定颜色并单击

        Args:
            obj (AndroidBotMain): AndroidBotMain实例对象
            color (str): 颜色字符串，必须以 # 开头，例如：#008577
            sub_colors (list): 辅助定位的其他颜色 (偏移x、偏移y、颜色字符串)
            region (tuple): 查找区域 (x1,y1,x2,y2)，默认全屏
            similarity (float): 相似度，0-1 的浮点数，默认 0.9
            wait_time (float): 等待时间，默认取 0.01秒只执行一次
            interval_time (float): 轮询间隔时间，默认取 0秒
            target_coord (Optional[Tuple[int, int]]): 强制指定点击坐标(x,y)，优先级高于自动计算坐标

        Returns:
            bool: 找到颜色并点击成功返回True，否则返回False

        Examples:
            >>> # 使用查找坐标点击
            >>> 找色_单击(bot, "#FF0000")
            
            >>> # 使用指定坐标点击
            >>> 找色_单击(bot, "#FF0000", target_coord=(100, 200))
        """
        # 分辨率转换处理
        if hasattr(obj, 'resolution_b') and isinstance(obj.resolution_b, tuple) and len(obj.resolution_b) == 2:
            if region != (0, 0, 0, 0):
                region = obj.coordinate_transform(region, obj.resolution_a, obj.resolution_b)
                region = tuple(int(x) for x in region)  # 确保region为整数
        
        # 直接调用find_color(它已包含循环和分辨率处理)
        coord = obj.find_color(color, sub_colors, region, similarity, wait_time, interval_time)
        print(f"[调试] find_color返回坐标: {coord}, 类型: {type(coord[0]) if coord else 'None'}")
        
        # find_color可能返回：
        # - (x,y): 找到颜色的坐标（可能是字符串或数字）
        # - (): 未找到颜色
        # 只有当两者(coord和target_coord)都无效时才返回False
        if not coord and not target_coord:
            return False
            
        # 处理target_coord的分辨率转换（find_color返回的坐标已处理过）
        if target_coord and hasattr(obj, 'resolution_b') and isinstance(obj.resolution_b, tuple):
            coord = obj.coordinate_transform(target_coord, obj.resolution_a, obj.resolution_b)
            coord = tuple(int(x) for x in coord)
        
        # 验证并处理坐标
        if coord and isinstance(coord, tuple) and len(coord) == 2:
            try:
                # 处理字符串或数字类型的坐标
                if isinstance(coord[0], str):
                    coord = (float(coord[0]), float(coord[1]))
                elif not isinstance(coord[0], (int, float)):
                    return False
                    
                coord = cls.random_offset(coord)
            except (ValueError, TypeError):
                print(f"[错误] 坐标转换失败: {coord}")
                return False
        else:
            return False
        
        # 执行点击
        if obj.device_mode == "HID":
            obj.hid_click(coord)
            日志.log_message(f"{obj.android_id} HID点击  {coord}", "调试")
        elif obj.device_mode == "无障碍":
            obj.click(coord)
            日志.log_message(f"{obj.android_id} 无障碍点击  {coord}", "调试")
        else:
            日志.log_message(f"{obj.android_id} 设备模式异常", "错误")
            return False
        
        print(f"[{obj.android_id}] 查找并点击颜色: {color} | 坐标: {coord}")
        return True

    @classmethod
    def 单击_坐标(cls, obj, coord: tuple) -> bool:
        """
        在屏幕上点击指定坐标（支持分辨率转换）

        Args:
            cls: 类本身（如果不需要可以忽略）
            obj (AndroidBotMain): AndroidBotMain实例对象
            coord (tuple): 要点击的目标坐标(x,y)

        Returns:
            bool: 点击成功返回True，否则返回False

        Raises:
            ValueError: 当coord参数无效时抛出

        Examples:
            >>> 单击_坐标(None, bot, (100, 200))
        """
        try:
            # 参数验证
            if not isinstance(coord, (tuple, list)) or len(coord) != 2:
                raise ValueError("coord必须是包含两个数字的元组或列表")
            if not all(isinstance(x, (int, float)) for x in coord):
                raise ValueError("coord中的值必须是数字")

            # 调试信息
            print(f"[调试] 原始坐标: {coord}")
            print(f"[调试] 分辨率A: {getattr(obj, 'resolution_a', '未设置')}")
            print(f"[调试] 分辨率B: {getattr(obj, 'resolution_b', '未设置')}")

            # 分辨率转换处理
            try:
                # 确保使用正确的分辨率参数顺序
                target_coord = obj.coordinate_transform(coord, obj.resolution_a, obj.resolution_b)
                # 添加随机偏移(与找字_匹配_单击一致)
                target_coord = cls.random_offset(tuple(int(round(x)) for x in target_coord))
                print(f"[调试] 转换后坐标: {target_coord}")
            except AttributeError:
                raise ValueError("obj缺少coordinate_transform方法或resolution_a/resolution_b属性")
            except Exception as e:
                raise ValueError(f"坐标转换失败: {str(e)}")

            # 执行点击
            try:
                if obj.device_mode == "HID":
                    obj.hid_click(target_coord)
                    print(f"[{getattr(obj, 'android_id', '未知设备')}] HID点击坐标: {target_coord}")
                elif obj.device_mode == "无障碍":
                    obj.click(target_coord)
                    print(f"[{getattr(obj, 'android_id', '未知设备')}] 无障碍点击坐标: {target_coord}")
                else:
                    print(f"[{getattr(obj, 'android_id', '未知设备')}] 设备模式异常: {obj.device_mode}")
                    return False

                # 记录日志
                日志.log_message(f"{getattr(obj, 'android_id', '未知设备')} 点击坐标: {target_coord}", "调试")
                return True

            except Exception as e:
                print(f"[{getattr(obj, 'android_id', '未知设备')}] 点击执行失败: {str(e)}")
                return False

        except ValueError as ve:
            print(f"[{getattr(obj, 'android_id', '未知设备')}] 参数错误: {str(ve)}")
            return False
        except Exception as e:
            print(f"[{getattr(obj, 'android_id', '未知设备')}] 点击过程中发生未知错误: {str(e)}")
            return False
    @classmethod
    def find_text_bool(
        cls,
        obj,
        text: str,
        region: Tuple[int, int, int, int] = (0, 0, 0, 0),
        algorithm: Tuple[int, int, int] = (0, 0, 0),
        scale: float = 1.0,
        timeout: int = 0,
        match_mode: str = "any_char"  # 新增匹配模式参数
    ) -> bool:
        """
        支持三种匹配模式的文本查找函数
        
        Args:
            match_mode: 可选模式:
                - "exact": 完全匹配（文本必须完全相同）
                - "substring": 子串匹配（识别文本包含目标文本）
                - "any_char": 任意字符匹配（默认，识别文本包含目标文本中任意一个字符）
        """
        start_time = time.time()
        
        # 空文本快速返回
        if not text:
            return False

        # 分辨率转换
        if hasattr(obj, 'resolution_b') and isinstance(obj.resolution_b, tuple):
            if region != (0, 0, 0, 0):
                region = obj.coordinate_transform(region, obj.resolution_a, obj.resolution_b)
                region = tuple(int(x) for x in region)

        while True:
            ocr_results = obj._parsing_ocr_data(region, algorithm, scale)
            # print("find_text_bool: ", ocr_results)
            if ocr_results:
                for item in ocr_results:
                    if len(item) >= 2 and isinstance(item[1], list):
                        recognized_text = item[1][0]
                        
                        # 根据模式选择匹配逻辑
                        if match_mode == "exact":
                            matched = (text == recognized_text)
                        elif match_mode == "substring":
                            matched = (text in recognized_text)
                        else:  # 默认 any_char
                            matched = any(char in recognized_text for char in text)
                        
                        if matched:
                            print(f"find_text_bool [{match_mode}匹配] 目标:'{text}' | 识别:'{recognized_text}'")
                            return True
            
            # 超时处理
            if timeout > 0 and (time.time() - start_time) >= timeout:
                return False
            if timeout <= 0:
                return False
    @classmethod
    def get_text(cls, obj, region=(0,0,0,0), algorithm=(0,0,0), scale=1, timeout=0):
        """获取匹配文本列表
        :param obj: AndroidBotMain实例
        :param region: 查找区域 (x1,y1,x2,y2)，默认全屏
        :param algorithm: 二值化
        :param scale: 图片缩放比例，默认1
        :param timeout: 超时时间(秒)，0表示只查找一次
        :return: 返回获取的内容
        """
        start_time = time.time()
        # 分辨率转换处理
        if region != (0,0,0,0) and hasattr(obj, 'resolution_b') and \
           isinstance(obj.resolution_b, tuple) and len(obj.resolution_b) == 2:
            region = obj.coordinate_transform(region, obj.resolution_a, obj.resolution_b)
            region = tuple(int(x) for x in region)  # 确保region为整数
        while True:
            text_list = obj.get_text(region, algorithm, scale)
            if text_list:
                日志.log_message(f"[{obj.android_id}] get_text获取成功:{text_list})", "成功")
                return text_list
            else:
                current_time = time.time()
                elapsed = current_time - start_time
                if timeout <= 0 or elapsed >= timeout:
                    return False
