from Aibote import Android
from window_func import 连接管理,账号管理
from .剑三通用函数 import 区服函数
import time
import random
#获取随机字符用来取名字使用
def get_random_common_chinese_char():
    # GB2312 一级汉字范围：0xB0A1-0xD7F9（共 3755 个常用汉字）
    # 随机选取一个码位，并转换为汉字
    while True:
        # 随机选择区码（0xB0-0xD7）和位码（0xA1-0xFE）
        zone = random.randint(0xB0, 0xD7)
        bit = random.randint(0xA1, 0xFE)
        # 组合成 GB2312 编码
        gb2312_code = (zone << 8) | bit
        # 转换为 Unicode（GB2312 -> UTF-8）
        try:
            char = bytes([zone, bit]).decode('gb2312')
        except UnicodeDecodeError:
            continue  # 跳过无效编码
        
        # 过滤不文明字（示例）
        banned_chars = {'傻', '逼', '屌', '操', '日', '妈', '鸡', '奸', '婊', '贱'}
        if char not in banned_chars:
            return char
class 登录区服:
    @staticmethod    
    def 关闭性能提示(obj):
        """关闭性能不足提示弹窗"""
        if Android.找字_匹配_单击(obj, "确认", region=(1146, 756, 1254, 822)):
            连接管理.update_device_action_by_id(obj.android_id, "性能低点击")
            return True
    def 关闭公告弹窗(obj):
        """关闭性能不足提示弹窗"""
        if Android.找字_匹配_单击(obj, "公告", region=(543, 471, 642, 540),target_coord=(1988, 117)):
            连接管理.update_device_action_by_id(obj.android_id, "关闭公告弹窗")
            return True
    @staticmethod        
    def 处理区服选择(obj):
        """处理区服选择和状态检查"""
        text_list = Android.get_text(obj, region=(921, 567, 1448, 631), scale=1.1)
        print(text_list)
        if text_list:
            区服名称 = 区服函数.匹配区服名称(text_list[0])
            print(区服名称)
            if 区服名称:
                obj.登录区服 = 区服名称
                get = 区服函数.检查区服任务状态(obj.android_id, 区服名称)
                if get["status"] == True and get["need_execute"] == True:
                    print("任务已完成")
                    return 11
                elif get["status"] == True and get["need_execute"] == False: 
                    print("任未完成")
                    #更新账号管理登录区服和UI
                    账号管理.update_account_server_by_device(obj.android_id, 区服名称)
                    #更新连接管理区服
                    连接管理.update_device_server_by_id(obj.android_id, 区服名称)
                    return 12
            else:
                print("查询区服标签数据库失败")
    @staticmethod            
    def 点击登录按钮(obj):
        """点击登录游戏按钮"""
        if Android.找字_匹配_单击(obj, "登录", region=(1092, 706, 1203, 778)):
            连接管理.update_device_action_by_id(obj.android_id, "点击登录游戏")
            return True
    @staticmethod            
    def 点击取消(obj):
        """点击取消"""
        if Android.找字_匹配_单击(obj, "取消", region=(1011, 766, 1086, 813)):
            连接管理.update_device_action_by_id(obj.android_id, "点击取消")
            return True


class 创建角色:
    @staticmethod
    def 点击纯阳门派(obj):
        """点击纯阳门派"""
        if Android.找字_匹配_单击(obj, "纯阳", region=(327, 232, 407, 283)):
            连接管理.update_device_action_by_id(obj.android_id, "纯阳点击")
            return True

    @staticmethod            
    def 点击下一步一(obj):
        """点击下一步"""
        if Android.找字_匹配_单击(obj, "紫霞", region=(1949, 484, 2030, 538),target_coord=(2024, 990)):
            连接管理.update_device_action_by_id(obj.android_id, "点击下一步一")
            return True
    @staticmethod            
    def 点击下一步二(obj):
        """点击下一步"""
        if Android.找字_匹配_单击(obj, "预设", region=(178, 7, 288, 83),target_coord=(2024, 990)):
            连接管理.update_device_action_by_id(obj.android_id, "点击下一步二")
            return True
    @staticmethod            
    def 点击下一步三(obj):
        """点击下一步"""
        if Android.找字_匹配_单击(obj, "易容", region=(176, 9, 280, 75),target_coord=(2024, 990)):
            连接管理.update_device_action_by_id(obj.android_id, "点击下一步三")
            return True

    @staticmethod            
    def 点击随机名字(obj):
        """点击随机名字"""
        if Android.find_text_click(obj, "名字", region=(1656, 250, 1766, 315),target_coord=(1913, 385)):
            Android.单击_坐标(obj,(1698, 383))
            time.sleep(2)
            obj.send_keys(get_random_common_chinese_char())
            time.sleep(2)
            obj.send_vk(66) #回车键
            time.sleep(2)
            Android.单击_坐标(obj,(1950, 993)) #点击确定
            连接管理.update_device_action_by_id(obj.android_id, "随机名字")
            return True
    @staticmethod            
    def 进入游戏(obj):
        """进入游戏"""
        if Android.找字_匹配_单击(obj, "进入游戏", region=(1919, 968, 2115, 1032)):
            连接管理.update_device_action_by_id(obj.android_id, "进入游戏")
            return True
    def 点卡不足检测(obj):
        """点卡不足检测"""
        if Android.find_text_bool(obj, "中心", region=(1378, 764, 1466, 819)):
            连接管理.update_device_action_by_id(obj.android_id, "点卡不足检测")
            return True

class 选择区服:
    @staticmethod
    def 进入选择区服点击(obj):
        """选择区服点击"""
        #找登录界面的卡自,点击指定坐标1428, 598
        if Android.找字_匹配_单击(obj, "点卡服", region=(1196, 569, 1372, 629),target_coord=(1422, 595)):
            连接管理.update_device_action_by_id(obj.android_id, "选区服点击")
            return True

    @staticmethod            
    def 点击电信区(obj):
        """点击电信区"""
        if Android.找字_匹配_单击(obj, "电信区", region=(232, 406, 365, 470)):
            连接管理.update_device_action_by_id(obj.android_id, "点击电信区")
            return True
    @staticmethod            
    def 点击指定区服(obj,区服):
        """点击指定区服"""
        if Android.找字_匹配_单击(obj, 区服, region=(476, 95, 2241, 347)):
            连接管理.update_device_action_by_id(obj.android_id, "点击指定区服")
            return True

    @staticmethod            
    def 确认选择点击(obj):
        """确认选择点击"""
        if Android.找字_匹配_单击(obj, "确认选择", region=(1861, 976, 2026, 1042)):
            连接管理.update_device_action_by_id(obj.android_id, "确认选择")
            return True

