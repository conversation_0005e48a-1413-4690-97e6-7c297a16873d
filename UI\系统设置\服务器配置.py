from PyQt5.QtCore import Qt, QRegExp
from PyQt5.QtGui import QRegExpValidator, QIntValidator
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QLineEdit, QPushButton, QFrame)
import winreg

class ServerConfigTab(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.main_window = parent
        self.init_ui()

    def init_ui(self):
        """初始化服务器配置UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(5)
        
        # 服务器配置区域
        server_group = QFrame()
        server_group.setStyleSheet("""
            QFrame {
                background-color: rgba(20, 25, 45, 0.5);
                border-radius: 8px;
                border: 1px solid rgba(0, 245, 255, 0.2);
                padding: 15px;
            }
        """)
        
        # 尝试从注册表读取配置
        try:
            key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, r"Software\Aibote\ServerConfig")
            default_ip = winreg.QueryValueEx(key, "ServerIP")[0]
            default_port = winreg.QueryValueEx(key, "ServerPort")[0]
            winreg.CloseKey(key)
        except:
            default_ip = "127.0.0.1"
            default_port = "7878"
        
        server_layout = QVBoxLayout(server_group)
        server_layout.setContentsMargins(0, 0, 0, 0)
        server_layout.setSpacing(15)
        
        # 标题
        title = QLabel("服务器配置")
        title.setStyleSheet("""
            QLabel {
                color: #00F5FF;
                font-size: 16px;
                font-weight: bold;
            }
        """)
        server_layout.addWidget(title)
        
        # IP配置
        ip_layout = QHBoxLayout()
        ip_layout.setSpacing(10)
        
        ip_label = QLabel("IP地址:")
        ip_label.setStyleSheet("color: #00F5FF;")
        
        self.ip_input = QLineEdit(default_ip)
        self.ip_input.setStyleSheet("""
            QLineEdit {
                color: #00F5FF;
                background-color: rgba(106, 17, 203, 0.3);
                border: 1px solid rgba(0, 245, 255, 0.3);
                border-radius: 4px;
                padding: 6px 8px;
            }
        """)
        self.ip_input.setValidator(QRegExpValidator(QRegExp(
            r"^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$"
        )))
        
        ip_layout.addWidget(ip_label)
        ip_layout.addWidget(self.ip_input)
        server_layout.addLayout(ip_layout)
        
        # 端口配置
        port_layout = QHBoxLayout()
        port_layout.setSpacing(10)
        
        port_label = QLabel("端口号:")
        port_label.setStyleSheet("color: #00F5FF;")
        
        self.port_input = QLineEdit(default_port)
        self.port_input.setStyleSheet(self.ip_input.styleSheet())
        self.port_input.setValidator(QIntValidator(1024, 65535))
        
        port_layout.addWidget(port_label)
        port_layout.addWidget(self.port_input)
        server_layout.addLayout(port_layout)
        
        # 保存按钮
        save_btn = QPushButton("保存配置")
        save_btn.setStyleSheet("""
            QPushButton {
                color: #00FF00;
                background-color: rgba(106, 17, 203, 0.3);
                border: 1px solid #00FF00;
                border-radius: 4px;
                padding: 8px 15px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: rgba(0, 255, 0, 0.5);
            }
        """)
        save_btn.clicked.connect(self.save_settings)
        
        server_layout.addWidget(save_btn, 0, Qt.AlignRight)
        layout.addWidget(server_group, 1)  # 添加伸缩因子1使内容撑开高度

    def save_settings(self):
        """保存服务器配置到内存和注册表"""
        try:
            ip = self.ip_input.text()
            port = self.port_input.text()
            
            if not ip or not port:
                raise ValueError("IP和端口不能为空")
            
            # 保存到注册表
            try:
                key = winreg.CreateKey(winreg.HKEY_CURRENT_USER, r"Software\Aibote\ServerConfig")
                winreg.SetValueEx(key, "ServerIP", 0, winreg.REG_SZ, ip)
                winreg.SetValueEx(key, "ServerPort", 0, winreg.REG_SZ, port)
                winreg.CloseKey(key)
                if self.main_window:
                    self.main_window.append_output("系统", f"配置已保存到注册表: {ip}:{port}")
            except Exception as reg_error:
                if self.main_window:
                    self.main_window.append_output("警告", f"无法保存到注册表: {str(reg_error)}")
                    
            # 保存到内存
            if self.main_window:
                self.main_window.append_output("系统", f"服务器配置已保存: {ip}:{port}")
            
        except Exception as e:
            if self.main_window:
                self.main_window.append_output("错误", f"保存配置失败: {str(e)}")

    def get_server_info(self):
        """获取服务器配置信息"""
        return self.ip_input.text(), int(self.port_input.text())
