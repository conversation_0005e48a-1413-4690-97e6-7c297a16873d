from PyQt5.QtWidgets import QVBoxLayout, QTabWidget
from .服务器配置 import ServerConfigTab
from .数据库管理 import DatabaseTab

class SystemSettingsPage(QTabWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.main_window = parent
        self.init_ui()

    def init_ui(self):
        """初始化系统设置UI"""
        # 连接tab切换信号
        self.currentChanged.connect(self.on_tab_changed)
        
        self.setStyleSheet("""
            QTabWidget::pane {
                border: none;
            }
            QTabBar::tab {
                color: #00F5FF;
                background: rgba(20, 25, 45, 0.5);
                padding: 8px 15px;
                border: 1px solid rgba(0, 245, 255, 0.2);
                border-bottom: none;
                border-top-left-radius: 5px;
                border-top-right-radius: 5px;
            }
            QTabBar::tab:selected {
                background: rgba(106, 17, 203, 0.3);
                border-color: rgba(0, 245, 255, 0.5);
            }
        """)
        
        # 添加服务器配置标签页
        self.server_tab = ServerConfigTab(self.main_window)
        self.addTab(self.server_tab, "服务器配置")
        
        # 添加数据库管理标签页
        self.db_tab = DatabaseTab(self.main_window)
        self.addTab(self.db_tab, "数据库管理")

    def get_server_info(self):
        """获取服务器配置信息"""
        return self.server_tab.get_server_info()
        
    def on_tab_changed(self, index):
        """处理tab切换事件"""
        if index == 1:  # 数据库管理tab
            self.db_tab.load_tables()

__all__ = ['SystemSettingsPage']
