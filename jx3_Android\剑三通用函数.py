import difflib
import sqlite3
import random
from pathlib import Path
from datetime import datetime
from window_func import 日志, 连接管理
from Aibote import Android

class 区服函数:
    @staticmethod
    def 匹配区服名称(ocr_text, 相似度阈值=0.6):
        """
        模糊匹配区服名称
        :param ocr_text: OCR识别出的区服名称(可能包含特殊字符)
        :param 相似度阈值: 匹配相似度阈值(0-1)，默认0.6
        :return: 匹配到的标准区服名称，如无匹配则返回None
        """
        try:
            # 获取所有标准区服名称
            db_path = Path(__file__).parent.parent / "DB" / "account.db"
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT 标签名称 FROM 区服标签")
            所有区服 = [row[0] for row in cursor.fetchall()]
            conn.close()

            # 预处理OCR文本：去除特殊字符和括号内容
            清洗文本 = ocr_text.split('(')[0].strip()
            
            # 模糊匹配
            匹配结果 = difflib.get_close_matches(
                清洗文本,
                所有区服,
                n=1,
                cutoff=相似度阈值
            )
            return 匹配结果[0] if 匹配结果 else None
            
        except Exception as e:
            print(f"匹配区服名称出错: {str(e)}")
            return None

    @staticmethod
    def 检查区服任务状态(设备ID, 区服名称):
        try:
            db_path = Path(__file__).parent.parent / "DB" / "account.db"
            conn = None
            try:
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                
                # 临时调试：查询账号表结构
                cursor.execute("PRAGMA table_info(账号表)")
                columns = cursor.fetchall()
                日志.log_message(f"账号表结构: {columns}", "调试")
                
                # 1. 更新账号表的登录区服
                cursor.execute("""
                    UPDATE 账号表 
                    SET 登录区服=?
                    WHERE 设备ID=?
                """, (区服名称, 设备ID))
                
                # 2. 查询区服任务状态(从区服表)
                cursor.execute("SELECT id FROM 账号表 WHERE 设备ID=?", (设备ID,))
                账号结果 = cursor.fetchone()
                if not 账号结果:
                    return {"status": False, "need_execute": False, "message": "未找到设备ID对应的账号"}
                账号ID = 账号结果[0]
                
                cursor.execute("""
                    SELECT 完成状态, 完成日期 FROM 区服表 
                    WHERE 账号ID=? AND 区服名称=?
                """, (账号ID, 区服名称))
                区服结果 = cursor.fetchone()
                
                if not 区服结果:  # 无记录
                    return {"status": False, "need_execute": False, "message": "账号在区服无记录"}
                    
                完成状态, 完成日期 = 区服结果
                
                if 完成状态 == "是":
                    # 检查完成日期
                    当天日期 = datetime.now().strftime("%Y-%m-%d")
                    if 完成日期 == 当天日期:
                        return {"status": True, "need_execute": True, "message": "任务已完成"}
                    else:
                        return {"status": True, "need_execute": False, "message": "任务未完成"}
                return {"status": True, "need_execute": False, "message": "任务未完成"}
                
            except sqlite3.Error as e:
                error_msg = f"检查区服任务状态失败\n查询语句: SELECT id FROM 账号表 WHERE 设备ID=?\n错误详情: {str(e)}"
                print(f"[数据库错误] {error_msg}")
                return {"status": False, "need_execute": False, "message": "数据库操作失败"}
            finally:
                if conn:
                    conn.close()
                    
        except Exception as e:
            print(f"[系统错误] 检查区服任务状态失败\n错误详情: {str(e)}")
            return {"status": False, "need_execute": False, "message": f"系统错误: {str(e)}"}

    @staticmethod
    def 随机获取未完成区服名称(device_id):
        """
        随机获取未完成或今日未完成的区服名称
        :param device_id: 设备ID
        :return: 随机区服名称，如无匹配则返回None
        """
        try:
            db_path = Path(__file__).parent.parent / "DB" / "account.db"
            conn = None
            try:
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                
                # 1. 通过设备ID获取账号ID
                # 确保device_id转换为字符串匹配数据库中的TEXT类型
                device_id_str = str(device_id)
                日志.log_message(f"查询设备ID: {device_id_str}", "调试")
                
                cursor.execute("SELECT id FROM 账号表 WHERE 设备ID=?", (device_id_str,))
                账号结果 = cursor.fetchone()
                
                if not 账号结果:
                    # 调试：查询所有设备ID检查匹配问题
                    cursor.execute("SELECT 设备ID FROM 账号表")
                    所有设备ID = [row[0] for row in cursor.fetchall()]
                    日志.log_message(f"设备 {device_id_str} 未找到对应账号 (现有设备ID: {所有设备ID})", "警告")
                    return None
                
                账号ID = 账号结果[0]
                日志.log_message(f"找到账号ID: {账号ID} 对应设备ID: {device_id_str}", "调试")
                
                # 2. 获取未完成或今日未完成的区服列表
                当天日期 = datetime.now().strftime("%Y-%m-%d")
                cursor.execute("""
                    SELECT 区服名称 FROM 区服表 
                    WHERE 账号ID=? AND (
                        完成状态 IN ('否', '未完成') OR 
                        (完成状态='是' AND 完成日期!=?)
                    )
                    """, (账号ID, 当天日期))
                
                # 获取查询结果并记录调试信息
                区服列表 = [row[0] for row in cursor.fetchall()]
                日志.log_message(f"账号 {账号ID} 的未完成任务区服列表: {区服列表}", "调试")
                
                if not 区服列表:
                    # 调试：查询该账号所有区服记录
                    cursor.execute("SELECT 区服名称, 完成状态, 完成日期 FROM 区服表 WHERE 账号ID=?", (账号ID,))
                    所有区服 = cursor.fetchall()
                    日志.log_message(f"账号 {账号ID} 的所有区服记录: {所有区服}", "调试")
                    return None
                
                # 3. 更新账号表的登录区服
                随机区服 = random.choice(区服列表)
                cursor.execute("""
                    UPDATE 账号表 
                    SET 登录区服=?
                    WHERE 设备ID=?
                """, (随机区服, device_id_str))
                
                if not 区服列表:
                    日志.log_message(f"账号 {账号ID} 无符合条件的区服", "信息")
                    return None
                    
                # 3. 随机返回一个区服
                随机区服 = random.choice(区服列表)
                日志.log_message(f"为设备 {device_id} 随机选择区服: {随机区服}", "调试")
                return 随机区服
                
            except sqlite3.Error as e:
                error_msg = f"随机获取未完成区服失败\n设备ID: {device_id}\n错误详情: {str(e)}"
                日志.log_message(error_msg, "错误")
                return None
            finally:
                if conn:
                    conn.close()
                    
        except Exception as e:
            日志.log_message(f"[系统错误] 随机获取未完成区服失败\n设备ID: {device_id}\n错误详情: {str(e)}", "错误")
            return None
class 游戏内通用:
    @staticmethod
    def 跳过剧情点击(obj):
         if Android.找色_单击(obj,"#000000",[[16, 10, "#000000"], [16, 16, "#000000"], [24, 20, "#000000"]],(2094, 21, 2177, 65),1):
            连接管理.update_device_action_by_id(obj.android_id, "跳过剧情点击")
            return True

    @staticmethod
    def 弹窗_确认_点击(obj):
         if Android.找字_匹配_单击(obj, "确认", region=(1303, 762, 1400, 817)):
            连接管理.update_device_action_by_id(obj.android_id, "弹窗确认点击")
            return True