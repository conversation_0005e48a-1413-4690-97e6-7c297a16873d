from PyQt5.QtCore import QTimer, Qt, QThread, pyqtSignal
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QProgressBar, QPushButton)
import psutil
import os
try:
    import GPUtil
    HAS_GPU = True
except ImportError:
    HAS_GPU = False

class SystemMonitor(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.last_values = {'cpu': 0, 'mem': 0, 'gpu': 0}  # 保存最后的值
        self.is_monitoring = False  # 监控状态标志
        self.init_ui()
        self.init_timer()

    def init_ui(self):
        """初始化监控UI"""
        self.setStyleSheet("""
            background-color: rgba(30, 35, 60, 0.5);
            border-top: 1px solid rgba(0, 245, 255, 0.2);
            padding: 0;
        """)
        
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(5)
        
        # 添加开关按钮
        self.toggle_btn = QPushButton("开启监控")
        self.toggle_btn.setCheckable(True)
        self.toggle_btn.setStyleSheet("""
            QPushButton {
                color: #FF5555;
                background-color: rgba(106, 17, 203, 0.3);
                border: 1px solid #FF5555;
                border-radius: 4px;
                padding: 2px 5px;
                font-size: 8px;
                min-width: 60px;
            }
            QPushButton:checked {
                color: #00FF00;
                border: 1px solid #00FF00;
            }
        """)
        self.toggle_btn.clicked.connect(self.toggle_monitoring)
        main_layout.addWidget(self.toggle_btn, 0, Qt.AlignHCenter)
        
        # 监控指标布局
        layout = QHBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(5)
        main_layout.addLayout(layout)
        
        # CPU监控
        cpu_container = QWidget()
        cpu_layout = QVBoxLayout(cpu_container)
        cpu_layout.setContentsMargins(0, 0, 0, 0)
        cpu_layout.setSpacing(2)
        
        self.cpu_label = QLabel("CPU")
        self.cpu_label.setStyleSheet("color: #00F5FF; font-size: 8px; margin: 0; padding: 0;")
        
        self.cpu_bar = QProgressBar()
        self.cpu_bar.setRange(0, 100)
        self.cpu_bar.setFormat("%p%")
        self.cpu_bar.setOrientation(Qt.Horizontal)
        self.cpu_bar.setStyleSheet(self.get_progressbar_style("#00F5FF"))
        
        cpu_layout.addWidget(self.cpu_label, 0, Qt.AlignHCenter)
        cpu_layout.addWidget(self.cpu_bar, 0, Qt.AlignHCenter)
        
        # 内存监控
        mem_container = QWidget()
        mem_layout = QVBoxLayout(mem_container)
        mem_layout.setContentsMargins(0, 0, 0, 0)
        mem_layout.setSpacing(2)
        
        self.mem_label = QLabel("内存")
        self.mem_label.setStyleSheet("color: #FFA500; font-size: 8px; margin: 0; padding: 0;")
        
        self.mem_bar = QProgressBar()
        self.mem_bar.setRange(0, 100)
        self.mem_bar.setFormat("%p%")
        self.mem_bar.setOrientation(Qt.Horizontal)
        self.mem_bar.setStyleSheet(self.get_progressbar_style("#FFA500"))
        
        mem_layout.addWidget(self.mem_label, 0, Qt.AlignHCenter)
        mem_layout.addWidget(self.mem_bar, 0, Qt.AlignHCenter)
        
        layout.addWidget(cpu_container)
        layout.addWidget(mem_container)
        
        # GPU监控 (如果可用)
        if HAS_GPU:
            gpu_container = QWidget()
            gpu_layout = QVBoxLayout(gpu_container)
            gpu_layout.setContentsMargins(0, 0, 0, 0)
            gpu_layout.setSpacing(2)
            
            self.gpu_label = QLabel("GPU")
            self.gpu_label.setStyleSheet("color: #FF00FF; font-size: 8px; margin: 0; padding: 0;")
            
            self.gpu_bar = QProgressBar()
            self.gpu_bar.setRange(0, 100)
            self.gpu_bar.setFormat("%p%")
            self.gpu_bar.setOrientation(Qt.Horizontal)
            self.gpu_bar.setStyleSheet(self.get_progressbar_style("#FF00FF"))
            
            gpu_layout.addWidget(self.gpu_label, 0, Qt.AlignHCenter)
            gpu_layout.addWidget(self.gpu_bar, 0, Qt.AlignHCenter)
            
            layout.addWidget(gpu_container)

    def get_progressbar_style(self, color):
        """获取进度条样式"""
        return f"""
            QProgressBar {{
                border: 1px solid {color};
                border-radius: 3px;
                text-align: center;
                color: white;
                background-color: rgba(30, 35, 60, 0.8);
                width: 100px;
                height: 20px;
            }}
            QProgressBar::chunk {{
                background-color: {color};
                width: 20px;
            }}
        """

    def init_timer(self):
        """初始化监控定时器"""
        self.worker = MonitorWorker()
        self.worker.data_updated.connect(self.update_ui)
        # 默认不启动
        
    def toggle_monitoring(self, checked):
        """切换监控状态"""
        self.is_monitoring = checked
        if checked:
            # 确保worker完全停止后再启动
            if hasattr(self.worker, 'running') and self.worker.running:
                self.worker.stop()
                self.worker.wait()
            self.worker = MonitorWorker()
            self.worker.data_updated.connect(self.update_ui)
            self.worker.start()
            self.toggle_btn.setText("关闭监控")
            # 强制刷新UI
            self.update_ui(*self.last_values.values())
        else:
            self.worker.stop()
            self.worker.wait()
            self.toggle_btn.setText("开启监控")
            # 显示暂停状态
            self.update_ui(*self.last_values.values())

    def update_ui(self, cpu_percent, mem_percent, gpu_percent):
        """更新UI显示"""
        # 保存最新值
        self.last_values = {
            'cpu': cpu_percent,
            'mem': mem_percent,
            'gpu': gpu_percent
        }
        
        if self.is_monitoring:
            # 监控中显示实时数据
            self.cpu_bar.setValue(int(cpu_percent))
            self.cpu_bar.setFormat(f"{cpu_percent:.1f}%")
            self.mem_bar.setValue(int(mem_percent))
            self.mem_bar.setFormat(f"{mem_percent:.1f}%")
            if HAS_GPU:
                self.gpu_bar.setValue(int(gpu_percent))
                self.gpu_bar.setFormat(f"{gpu_percent:.1f}%")
        else:
            # 暂停时显示最后值并添加"已暂停"提示
            self.cpu_bar.setValue(int(self.last_values['cpu']))
            self.cpu_bar.setFormat(f"{self.last_values['cpu']:.1f}% (已暂停)")
            self.mem_bar.setValue(int(self.last_values['mem']))
            self.mem_bar.setFormat(f"{self.last_values['mem']:.1f}% (已暂停)")
            if HAS_GPU:
                self.gpu_bar.setValue(int(gpu_percent))
                self.gpu_bar.setFormat(f"{gpu_percent:.1f}%")

class MonitorWorker(QThread):
    """监控工作线程"""
    data_updated = pyqtSignal(float, float, float)  # cpu, mem, gpu
    
    def __init__(self):
        super().__init__()
        self.running = True
        self.has_gpu = HAS_GPU
        
    def run(self):
        while self.running:
            # 获取系统CPU使用率(非阻塞模式)
            cpu_percent = psutil.cpu_percent(interval=None)
            # 获取系统内存使用率
            mem_percent = psutil.virtual_memory().percent
            
            gpu_percent = 0
            if self.has_gpu:
                try:
                    gpus = GPUtil.getGPUs()
                    if gpus:
                        gpu_percent = gpus[0].load * 100
                except Exception:
                    pass
            
            self.data_updated.emit(cpu_percent, mem_percent, gpu_percent)
            self.msleep(2000)  # 2秒刷新一次
            
    def stop(self):
        self.running = False
        self.wait()
