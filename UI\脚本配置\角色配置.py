from PyQt5.QtWidgets import QWidget, QVBoxLayout, QLabel
from PyQt5.QtCore import Qt

class CharacterConfigPage(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()

    def setup_ui(self):
        layout = QVBoxLayout(self)
        layout.setAlignment(Qt.AlignCenter)
        
        label = QLabel("角色配置功能开发中")
        label.setStyleSheet("""
            QLabel {
                color: #00F5FF;
                font-size: 16px;
                font-weight: bold;
            }
        """)
        layout.addWidget(label)
