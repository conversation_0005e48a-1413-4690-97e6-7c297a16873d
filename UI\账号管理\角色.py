from PyQt5.QtWidgets import (QTableWidget, QTableWidgetItem, QHeaderView)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QColor

class CharacterUI:
    def __init__(self, parent=None):
        self.parent = parent
        self.setup_ui()

    def setup_ui(self):
        self.char_table = QTableWidget()
        self.char_table.setColumnCount(4)
        self.char_table.setHorizontalHeaderLabels(["角色名称", "等级", "是否完成", "完成日期"])
        self.char_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.char_table.verticalHeader().setVisible(False)
        self.char_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.char_table.setStyleSheet("""
            QTableWidget {
                background-color: rgba(30, 35, 60, 0.5);
                border: 1px solid rgba(0, 245, 255, 0.2);
                color: #C0C0C0;
                gridline-color: rgba(0, 245, 255, 0.1);
            }
            QHeaderView::section {
                background-color: transparent;
                color: #00F5FF;
                padding-left: 8px;
                padding-right: 8px;
                border: none;
                font-size: 10pt;
            }
        """)
        self.show_no_character_data()

    def show_no_character_data(self):
        self.char_table.setRowCount(1)
        empty_item = QTableWidgetItem("暂无角色数据")
        empty_item.setTextAlignment(Qt.AlignCenter)
        empty_item.setFlags(Qt.ItemIsEnabled)
        empty_item.setForeground(QColor(150, 150, 150))
        self.char_table.setItem(0, 0, empty_item)
        self.char_table.setSpan(0, 0, 1, 4)

    def load_character_data(self, characters):
        self.char_table.clearContents()
        self.char_table.setRowCount(0)

        if not characters:
            self.show_no_character_data()
            return

        for row, char in enumerate(characters):
            self.char_table.insertRow(row)
            
            for col in range(3):  # 前3列保持不变
                item = QTableWidgetItem(char[col])
                item.setFlags(Qt.ItemIsEnabled | Qt.ItemIsSelectable)
                item.setTextAlignment(Qt.AlignCenter)
                if col == 2:  # 是否完成列
                    color = QColor(0, 200, 0) if char[col] == "是" else QColor(200, 0, 0)
                    item.setForeground(color)
                self.char_table.setItem(row, col, item)
            
            # 添加完成日期列
            date_item = QTableWidgetItem(char[3] if len(char) > 3 else "")
            date_item.setFlags(Qt.ItemIsEnabled | Qt.ItemIsSelectable)
            date_item.setTextAlignment(Qt.AlignCenter)
            self.char_table.setItem(row, 3, date_item)
            
            self.char_table.setRowHeight(row, 30)
