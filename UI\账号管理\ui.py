from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QTableWidget, QTableWidgetItem,
                            QHeaderView, QPushButton, QHBoxLayout, QMessageBox,
                            QFileDialog, QLabel, QDialog)
from PyQt5.QtCore import Qt, QEvent, pyqtSlot
from PyQt5.QtGui import QColor
from .账号 import AccountDialog, AccountManager
from .区服 import ServerManager
from window_func import 日志
import os

class AccountManagementPage(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        # 初始化事件过滤器
        self._event_filter_installed = False
        self.db_path = os.path.join("DB", "account.db")
        self.account_manager = AccountManager(self.db_path)
        self.server_manager = ServerManager(self.db_path)
        
        # 初始化时重置所有账号登录状态为"未登录"
        try:
            self.account_manager.reset_all_login_status("未登录")
            print("所有账号登录状态已重置为未登录")
        except Exception as e:
            print(f"重置登录状态失败: {str(e)}")
            
        self.setup_ui()
        self.load_data()

    def setup_ui(self):
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 账号表格
        self.table = QTableWidget()
        self.table.setContextMenuPolicy(Qt.NoContextMenu)
        self.table.setSelectionMode(QTableWidget.SingleSelection)
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        self.table.setColumnCount(10)
        self.table.setHorizontalHeaderLabels([
            "账号", "密码", "手机ID", "登录状态", "登录区服",
            "注册姓名", "手机", "身份证", "封号状态", "备注"
        ])
        self.table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.table.verticalHeader().setVisible(False)
        
        # 表格样式
        self.table.setStyleSheet("""
            QTableWidget {
                background-color: rgba(30, 35, 60, 0.5);
                border: 1px solid rgba(0, 245, 255, 0.2);
                color: #C0C0C0;
                font-family: "Segoe UI";
                font-size: 9pt;
                gridline-color: rgba(0, 245, 255, 0.1);
            }
            QTableWidget QTableCornerButton::section,
            QTableWidget::viewport {
                background-color: rgba(30, 35, 60, 0.5);
                border: none;
            }
            QHeaderView::section {
                background-color: transparent;
                color: #00F5FF;
                padding-left: 8px;
                padding-right: 8px;
                border: none;
                font-family: "Segoe UI";
                font-size: 10pt;
                font-weight: normal;
            }
            QTableWidget::item {
                padding: 5px;
                text-align: center;
                vertical-align: middle;
                selection-background-color: transparent;
                selection-color: inherit;
                outline: none;
                border: none;
                alignment: AlignCenter;
            }
            QTableWidget::item:selected {
                background-color: transparent;
                color: inherit;
                outline: none;
                border: none;
            }
            QTableWidget {
                outline: none;
            }
        """)
        
        # 操作按钮
        btn_layout = QHBoxLayout()
        btn_layout.setContentsMargins(5, 5, 5, 5)
        btn_layout.setSpacing(5)
        
        self.add_btn = QPushButton("添加")
        self.add_btn.setStyleSheet("""
            QPushButton {
                background-color: rgba(37, 117, 252, 0.3);
                color: #00F5FF;
                border: 1px solid #00F5FF;
                border-radius: 4px;
                padding: 5px 10px;
                min-width: 60px;
                font-weight: bold;
                font-size: 10px;
            }
            QPushButton:hover {
                background-color: rgba(0, 245, 255, 0.5);
                color: white;
                border: 1px solid white;
            }
        """)
        self.add_btn.clicked.connect(self.add_account)
        
        self.edit_btn = QPushButton("编辑")
        self.edit_btn.clicked.connect(self.edit_account)
        
        self.delete_btn = QPushButton("删除")
        self.delete_btn.clicked.connect(self.delete_account)
        
        self.export_btn = QPushButton("导出")
        self.export_btn.clicked.connect(self.export_accounts)

        self.import_btn = QPushButton("导入")
        self.import_btn.clicked.connect(self.import_accounts)

        self.template_btn = QPushButton("模板下载")
        self.template_btn.clicked.connect(self.download_template)

        # 设置按钮样式
        for btn in [self.edit_btn, self.delete_btn, self.export_btn, 
                   self.import_btn, self.template_btn]:
            btn.setStyleSheet(self.add_btn.styleSheet())
        
        btn_layout.addWidget(self.add_btn)
        btn_layout.addWidget(self.edit_btn)
        btn_layout.addWidget(self.delete_btn)
        btn_layout.addWidget(self.export_btn)
        btn_layout.addWidget(self.import_btn)
        btn_layout.addWidget(self.template_btn)
        
        # 主内容布局
        content_layout = QVBoxLayout()
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(0)
        content_layout.addWidget(self.table)
        
        main_layout.addLayout(content_layout)
        main_layout.addLayout(btn_layout)
        
        # 连接表格双击信号(仅账号列)
        self.table.itemDoubleClicked.connect(lambda item: 
            self.on_table_double_clicked(item) if item.column() == 0 else None)

    def add_account(self):
        dialog = AccountDialog("添加账号", self)
        dialog.setAttribute(Qt.WA_DeleteOnClose)
        if dialog.exec_() == QDialog.Accepted:
            try:
                account_data = dialog.get_account_data()
                if self.account_manager.account_exists(account_data['username']):
                    self.show_error(f"账号 {account_data['username']} 已存在")
                    return
                
                # 检查设备ID是否重复
                if account_data['device_id'] and self.account_manager.device_id_exists(account_data['device_id']):
                    self.show_error(f"设备ID {account_data['device_id']} 已存在")
                    return
                
                self.account_manager.add_account(account_data)
                self.load_data()
            except Exception as e:
                self.show_error(f"添加账号失败: {str(e)}")

    def edit_account(self):
        selected = self.table.currentRow()
        if selected == -1:
            self.show_warning("请先选择要编辑的行")
        else:
            try:
                username = self.table.item(selected, 0).text()
                account = self.account_manager.get_account_by_username(username)
                
                dialog = AccountDialog(f"编辑账号 - {username}", self, account)
                if dialog.exec_() == QDialog.Accepted:
                    account_data = dialog.get_account_data()
                    if account_data['username'] != username and \
                       self.account_manager.account_exists(account_data['username']):
                        self.show_error(f"账号 {account_data['username']} 已存在")
                        return
                    
                    # 检查设备ID是否重复
                    if account_data['device_id']:
                        old_device_id = account[3]  # 元组第3个元素是device_id
                        if (account_data['device_id'] != old_device_id and
                            self.account_manager.device_id_exists(account_data['device_id'])):
                            self.show_error(f"设备ID {account_data['device_id']} 已存在")
                            return
                    
                    self.account_manager.update_account(username, account_data)
                    self.load_data()
            except Exception as e:
                self.show_error(f"编辑账号失败: {str(e)}")

    def delete_account(self):
        selected_items = self.table.selectedItems()
        if not selected_items:
            self.show_warning("请先选择要删除的行")
        else:
            selected_rows = set(item.row() for item in selected_items)
            account_names = [self.table.item(row, 0).text() for row in selected_rows]
            
            confirm = QMessageBox(self)
            confirm.setWindowTitle("确认删除")
            confirm.setText(f"确定要删除以下账号吗?\n{', '.join(account_names)}")
            confirm.setIcon(QMessageBox.Question)
            confirm.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
            confirm.setAttribute(Qt.WA_DeleteOnClose)
            confirm.setStyleSheet(self.get_messagebox_style("warning"))
            
            if confirm.exec_() == QMessageBox.Yes:
                try:
                    for row in selected_rows:
                        username = self.table.item(row, 0).text()
                        self.account_manager.delete_account(username)
                    self.load_data()
                except Exception as e:
                    self.show_error(f"删除账号失败: {str(e)}")

    def export_accounts(self):
        try:
            accounts = self.account_manager.get_all_accounts()
            if not accounts:
                self.show_info("没有账号数据可导出")
                return

            file_path, filter = QFileDialog.getSaveFileName(
                self, "导出账号数据", "", 
                "Excel文件 (*.xlsx);;JSON文件 (*.json)"
            )
            if not file_path:
                return

            if filter == "Excel文件 (*.xlsx)":
                from openpyxl import Workbook
                wb = Workbook()
                ws = wb.active
                ws.title = "账号数据"
                headers = ["ID", "账号", "密码", "手机ID", "登录状态", 
                          "注册姓名", "手机", "身份证", "封号状态", "备注"]
                ws.append(headers)
                for row in accounts:
                    ws.append(row)
                wb.save(file_path)
            else:
                import json
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump([{
                        'username': row[1],
                        'password': row[2],
                        'device_id': row[3],
                        'login_status': row[4],
                        'real_name': row[5],
                        'phone': row[6],
                        'id_card': row[7],
                        'ban_status': row[8],
                        'remark': row[9]
                    } for row in accounts], f, ensure_ascii=False, indent=4)

            self.show_info(f"账号数据已成功导出到:\n{file_path}")
        except Exception as e:
            self.show_error(f"导出账号数据时出错:\n{str(e)}")

    def import_accounts(self):
        try:
            file_path, filter = QFileDialog.getOpenFileName(
                self, "导入账号数据", "", 
                "Excel文件 (*.xlsx);;JSON文件 (*.json)"
            )
            if not file_path:
                return

            accounts = []
            if filter == "Excel文件 (*.xlsx)":
                from openpyxl import load_workbook
                wb = load_workbook(file_path)
                ws = wb.active
                for row in ws.iter_rows(min_row=2, values_only=True):
                    accounts.append({
                        'username': row[1] if len(row) > 1 else '',
                        'password': row[2] if len(row) > 2 else '',
                        '手机ID': row[3] if len(row) > 3 else '',
                        'login_status': row[4] if len(row) > 4 else '',
                        'real_name': row[5] if len(row) > 5 else '',
                        'phone': row[6] if len(row) > 6 else '',
                        'id_card': row[7] if len(row) > 7 else '',
                        'ban_status': row[8] if len(row) > 8 else '',
                        'remark': row[9] if len(row) > 9 else ''
                    })
            else:
                import json
                with open(file_path, 'r', encoding='utf-8') as f:
                    accounts = json.load(f)

            if not accounts:
                self.show_info("导入文件为空")
                return

            for account in accounts:
                if not self.account_manager.account_exists(account['username']):
                    self.account_manager.add_account(account)

            self.load_data()
            self.show_info(f"成功导入 {len(accounts)} 条账号数据")
        except Exception as e:
            self.show_error(f"导入账号数据时出错:\n{str(e)}")

    def download_template(self):
        try:
            file_path, filter = QFileDialog.getSaveFileName(
                self, "保存模板文件", "账号模板", 
                "Excel文件 (*.xlsx);;JSON文件 (*.json)"
            )
            if not file_path:
                return

            if filter == "Excel文件 (*.xlsx)":
                from openpyxl import Workbook
                wb = Workbook()
                ws = wb.active
                ws.title = "账号模板"
                headers = ["ID", "账号", "密码", "手机ID", "登录状态", 
                          "注册姓名", "手机", "身份证", "封号状态", "备注"]
                ws.append(headers)
                ws.append([
                    "", "示例账号", "示例密码", "设备ID(可选)", "登录状态(可选)",
                    "真实姓名(可选)", "手机号(可选)", "身份证号(可选)", "封号状态(可选)", "备注信息(可选)"
                ])
                wb.save(file_path)
            else:
                import json
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump([{
                        "username": "示例账号",
                        "password": "示例密码",
                        "device_id": "设备ID(可选)",
                        "login_status": "登录状态(可选)",
                        "real_name": "真实姓名(可选)",
                        "phone": "手机号(可选)",
                        "id_card": "身份证号(可选)",
                        "ban_status": "封号状态(可选)",
                        "remark": "备注信息(可选)"
                    }], f, ensure_ascii=False, indent=4)

            self.show_info(f"账号模板已保存到:\n{file_path}")
        except Exception as e:
            self.show_error(f"下载模板时出错:\n{str(e)}")

    def on_table_double_clicked(self, item):
        from PyQt5.QtWidgets import QApplication
        if QApplication.mouseButtons() != Qt.LeftButton:
            return
            
        row = item.row()
        username = self.table.item(row, 0).text()
        
        try:
            account_id = self.account_manager.get_account_id(username)
            servers = self.server_manager.get_servers_by_account(account_id)
            
            # 检查是否已有服务器行
            if hasattr(self, 'server_row') and self.server_row == row:
                # 如果点击的是同一行，则隐藏服务器行
                self.hide_server_row()
                return
            
            # 隐藏已有的服务器行
            if hasattr(self, 'server_row'):
                self.hide_server_row()
            
            # 创建新的ServerUI实例
            from .区服 import ServerUI
            self.server_ui = ServerUI(servers, self.server_manager, self)
            self.server_ui.setStyleSheet("""
                background-color: rgba(30, 35, 60, 0.8);
                border: 1px solid rgba(0, 245, 255, 0.3);
            """)
            
            # 在选中行下方插入服务器行
            self.table.insertRow(row + 1)
            self.table.setSpan(row + 1, 0, 1, self.table.columnCount())
            self.table.setCellWidget(row + 1, 0, self.server_ui)
            
            # 设置行高适应内容
            height = self.server_ui.sizeHint().height()
            self.table.setRowHeight(row + 1, height)
            
            # 记录当前服务器行
            self.server_row = row
            
            # 重新选中账号行
            self.table.selectRow(row)
            
        except Exception as e:
            print(f"操作区服UI失败: {str(e)}")
            
    def hide_server_row(self):
        """隐藏服务器行并清理UI对象"""
        if hasattr(self, 'server_row'):
            try:
                row = self.server_row + 1
                if row < self.table.rowCount():
                    self.table.removeRow(row)
                del self.server_row
            except:
                pass
        if hasattr(self, 'server_ui'):
            try:
                self.server_ui.deleteLater()
                del self.server_ui
            except:
                pass
            
    def is_server_ui_valid(self):
        """检查ServerUI对象是否有效"""
        try:
            if hasattr(self, 'current_server_ui') and self.current_server_ui:
                # 尝试访问一个简单属性来验证对象是否有效
                _ = self.current_server_ui.objectName()
                return True
        except RuntimeError:
            return False
        return False

    def clear_all_server_ui(self):
        """清理所有服务器信息行"""
        self.hide_server_row()
        
    def clear_server_ui(self):
        """兼容方法，保持旧代码调用"""
        self.hide_server_row()
        
    def clear_server_info_rows(self):
        """兼容方法，保持旧代码调用"""
        self.hide_server_row()

    def show_error(self, message):
        msg = QMessageBox(self)
        msg.setWindowTitle("错误")
        msg.setText(message)
        msg.setIcon(QMessageBox.Critical)
        msg.setStandardButtons(QMessageBox.Ok)
        msg.setAttribute(Qt.WA_DeleteOnClose)
        msg.setStyleSheet(self.get_messagebox_style("error"))
        msg.exec_()

    def show_warning(self, message):
        msg = QMessageBox(self)
        msg.setWindowTitle("警告")
        msg.setText(message)
        msg.setIcon(QMessageBox.Warning)
        msg.setStandardButtons(QMessageBox.Ok)
        msg.setAttribute(Qt.WA_DeleteOnClose)
        msg.setStyleSheet(self.get_messagebox_style("warning"))
        msg.exec_()

    def show_info(self, message):
        msg = QMessageBox(self)
        msg.setWindowTitle("提示")
        msg.setText(message)
        msg.setIcon(QMessageBox.Information)
        msg.setStandardButtons(QMessageBox.Ok)
        msg.setAttribute(Qt.WA_DeleteOnClose)
        msg.setStyleSheet(self.get_messagebox_style("info"))
        msg.exec_()

    @pyqtSlot(str, str)
    def update_login_status(self, account: str, status: str):
        """更新指定账号的登录状态
        参数:
            account: 账号名称(QString)
            status: 登录状态(QString)
        """
        日志.log_message(f"开始更新账号状态: {account} -> {status}", "调试")
            
        # 更新数据库
        try:
            self.account_manager.update_login_status(account, status)
        except Exception as e:
            日志.log_message(f"更新数据库状态失败: {str(e)}", "错误")
            return False
            
        # 更新界面
        for row in range(self.table.rowCount()):
            item = self.table.item(row, 0)  # 第0列是账号
            if item:
                current_account = item.text()
                日志.log_message(f"检查第{row}行账号: {current_account}", "调试")
                
                if current_account == account:
                    日志.log_message(f"找到匹配账号: {current_account}", "调试")
                    status_item = QTableWidgetItem(status)
                    status_item.setTextAlignment(Qt.AlignCenter)
                    self.table.setItem(row, 3, status_item)  # 第3列是登录状态
                    
                    # 根据状态设置颜色
                    if status == "登录中":
                        status_item.setForeground(QColor(255, 255, 0))  # 黄色
                    elif status == "已登录":
                        status_item.setForeground(QColor(0, 255, 0))  # 绿色
                    elif status == "未登录":
                        status_item.setForeground(QColor(255, 0, 0))  # 红色
                    
                    # 获取设备ID并更新连接管理
                    device_id_item = self.table.item(row, 2)  # 第2列是手机ID
                    if device_id_item:
                        device_id = device_id_item.text()
                        日志.log_message(f"获取到设备ID: {device_id}", "调试")
                        if not device_id:
                            日志.log_message("设备ID为空，跳过更新连接管理", "警告")
                    else:
                        日志.log_message("未找到设备ID项", "警告")
                    
                    # 强制刷新表格
                    self.table.viewport().update()
                    return True
        
        # 重新加载数据并重试
        self.load_data()
        for row in range(self.table.rowCount()):
            item = self.table.item(row, 0)
            if item and item.text().strip().lower() == account.strip().lower():
                return self.update_login_status(account, status)
                
        return False

    def get_account_by_device_id(self, device_id: str) -> str:
        """
        通过设备ID获取关联账号
        参数:
            device_id: 要查询的设备ID
        返回:
            关联的账号名称，未找到返回空字符串
        """
        try:
            for row in range(self.table.rowCount()):
                device_item = self.table.item(row, 2)  # 第2列是设备ID
                if device_item and device_item.text() == device_id:
                    account_item = self.table.item(row, 0)  # 第0列是账号
                    if account_item:
                        return account_item.text()
            return ""
        except Exception as e:
            日志.log_message(f"通过设备ID查询账号失败: {str(e)}", "错误")
            return ""

    def load_data(self):
        try:
            # 清理现有的服务器行
            self.hide_server_row()
            self.table.setRowCount(0)
            accounts = self.account_manager.get_all_accounts()
            日志.log_message(f"从数据库加载到 {len(accounts)} 个账号", "调试")
            
            for account in accounts:
                row = self.table.rowCount()
                self.table.insertRow(row)
                日志.log_message(f"添加账号行 {row}: {account[1]}", "调试")
                
                # 添加账号数据行
                # 确保account元组有足够元素
                if len(account) >= 11:  # id + 10个字段
                    for col in range(10):
                        item = QTableWidgetItem(str(account[col+1] if account[col+1] is not None else ""))
                        item.setTextAlignment(Qt.AlignCenter)
                        self.table.setItem(row, col, item)
                else:
                    # 处理旧数据格式
                    for col in range(len(account)-1):  # 跳过id列
                        item = QTableWidgetItem(str(account[col+1] if account[col+1] is not None else ""))
                        item.setTextAlignment(Qt.AlignCenter)
                        self.table.setItem(row, col, item)
                    # 补充缺失的列
                    for col in range(len(account)-1, 10):
                        item = QTableWidgetItem("")
                        item.setTextAlignment(Qt.AlignCenter)
                        self.table.setItem(row, col, item)
                    if len(account) > 1:  # 账号列
                        日志.log_message(f"设置账号 {account[1]} 到表格 {row},0", "调试")
                
                # 设置单元格样式
                for col in range(self.table.columnCount()):
                    item = self.table.item(row, col)
                    item.setFlags(Qt.ItemIsEnabled | Qt.ItemIsSelectable)
                    if col == 7:  # 封号状态列特殊样式
                        if account[8] == "已封号":
                            item.setForeground(QColor(255, 85, 85))
                        elif account[8] == "正常":
                            item.setForeground(QColor(85, 255, 85))
                        else:
                            item.setForeground(QColor(255, 255, 85))
            
            # 调整行高
            for row in range(self.table.rowCount()):
                self.table.setRowHeight(row, 30)
                
        except Exception as e:
            self.show_error(f"加载账号数据失败: {str(e)}")

    def get_messagebox_style(self, msg_type="info"):
        styles = {
            "error": {
                "border_color": "#FF5555",
                "btn_bg_rgb": "255, 85, 85"
            },
            "warning": {
                "border_color": "#FFAA00", 
                "btn_bg_rgb": "255, 170, 0"
            },
            "info": {
                "border_color": "#00F5FF",
                "btn_bg_rgb": "0, 245, 255"
            }
        }
        
        style = styles.get(msg_type, styles["info"])
        
        return f"""
            QMessageBox {{
                background-color: rgba(20, 25, 45, 0.95);
                border: 2px solid {style["border_color"]};
                border-top: 3px solid qlineargradient(
                    x1:0, y1:0, x2:0, y2:1,
                    stop:0 {style["border_color"]}, stop:1 transparent
                );
            }}
            QLabel {{
                color: #FFFFFF;
                font-size: 14px;
                margin-top: 5px;
                font-weight: bold;
            }}
            QPushButton {{
                color: #FFFFFF;
                background-color: rgba({style["btn_bg_rgb"]}, 0.5);
                border: 1px solid {style["border_color"]};
                border-radius: 4px;
                padding: 5px 15px;
                min-width: 80px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: rgba({style["btn_bg_rgb"]}, 0.7);
                border: 1px solid white;
            }}
        """
        
        styles = {
            "error": {
                "border_color": "#FF5555",
                "btn_bg_rgb": "255, 85, 85"
            },
            "warning": {
                "border_color": "#FFAA00",
                "btn_bg_rgb": "255, 170, 0"
            },
            "info": {
                "border_color": "#00F5FF",
                "btn_bg_rgb": "0, 245, 255"
            }
        }
        
        style_config = styles.get(msg_type, styles["info"])
        return base_style.format(
            border_color=style_config["border_color"],
            btn_bg_rgb=style_config["btn_bg_rgb"]
        )

    # [Remaining methods remain unchanged...]
