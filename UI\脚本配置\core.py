from PyQt5.QtCore import Qt
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QLineEdit, QPushButton, QFrame, QTabWidget)

class ScriptConfigPage(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.main_window = parent
        self.init_ui()

    def init_ui(self):
        """初始化脚本配置UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(5)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: none;
            }
            QTabBar::tab {
                color: #00F5FF;
                background: rgba(20, 25, 45, 0.5);
                padding: 8px 15px;
                border: 1px solid rgba(0, 245, 255, 0.2);
                border-bottom: none;
                border-top-left-radius: 5px;
                border-top-right-radius: 5px;
            }
            QTabBar::tab:selected {
                background: rgba(106, 17, 203, 0.3);
                border-color: rgba(0, 245, 255, 0.5);
            }
        """)
        layout.addWidget(self.tab_widget)
        
        # 核心配置标签页
        core_tab = QWidget()
        core_tab_layout = QVBoxLayout(core_tab)
        core_tab_layout.setContentsMargins(0, 0, 0, 0)
        core_tab_layout.setSpacing(5)
        
        # 核心配置区域
        core_group = QFrame()
        core_group.setStyleSheet("""
            QFrame {
                background-color: rgba(20, 25, 45, 0.5);
                border-radius: 8px;
                border: 1px solid rgba(0, 245, 255, 0.2);
                padding: 15px;
            }
        """)
        
        core_layout = QVBoxLayout(core_group)
        core_layout.setContentsMargins(0, 0, 0, 0)
        core_layout.setSpacing(15)
        
        # 标题
        title = QLabel("核心配置")
        title.setStyleSheet("""
            QLabel {
                color: #00F5FF;
                font-size: 16px;
                font-weight: bold;
            }
        """)
        core_layout.addWidget(title)
        core_tab_layout.addWidget(core_group)
        core_tab_layout.addStretch()
        self.tab_widget.addTab(core_tab, "核心配置")
