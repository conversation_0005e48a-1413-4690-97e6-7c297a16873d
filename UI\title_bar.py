"""
标题栏组件模块
包含自定义标题栏、服务控制按钮、窗口控制按钮等
"""
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtWidgets import (QWidget, QHBoxLayout, QLabel, QPushButton, 
                            QMessageBox, QApplication)
from .styles import UIStyles, UIColors

class TitleBar(QWidget):
    """自定义标题栏组件"""
    
    # 定义信号
    minimize_requested = pyqtSignal()
    close_requested = pyqtSignal()
    start_service_requested = pyqtSignal()
    stop_service_requested = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.main_window = parent
        self.init_ui()
        
    def init_ui(self):
        """初始化标题栏UI"""
        self.setFixedHeight(50)
        self.setStyleSheet("background-color: transparent;")
        
        layout = QHBoxLayout(self)
        layout.setContentsMargins(10, 0, 10, 0)
        
        # 左侧标题
        self._create_title_label(layout)
        
        # 右侧服务控制区域
        self._create_service_controls(layout)
        
        # 窗口控制按钮
        self._create_window_controls(layout)
        
    def _create_title_label(self, layout):
        """创建标题标签"""
        title_label = QLabel("Aibote服务端控制中心")
        title_label.setStyleSheet(UIStyles.TITLE_LABEL_STYLE)
        layout.addWidget(title_label)
        
    def _create_service_controls(self, layout):
        """创建服务控制区域"""
        control_container = QWidget()
        control_layout = QHBoxLayout(control_container)
        control_layout.setContentsMargins(0, 0, 0, 0)
        control_layout.setSpacing(15)
        
        # 服务状态显示
        self.status_label = QLabel("服务未启动")
        self.status_label.setStyleSheet(UIStyles.STATUS_LABEL_STYLE)
        
        # 创建服务控制按钮
        self._create_service_buttons(control_layout)
        
        layout.addStretch()
        layout.addWidget(control_container)
        
    def _create_service_buttons(self, layout):
        """创建服务控制按钮"""
        self.start_btn = QPushButton("▶ 启动服务")
        self.stop_btn = QPushButton("■ 关闭服务")
        self.stop_btn.setEnabled(False)
        
        # 设置按钮样式
        self.start_btn.setStyleSheet(UIStyles.START_BUTTON_STYLE)
        self.stop_btn.setStyleSheet(UIStyles.STOP_BUTTON_STYLE)
        
        # 连接信号
        self.start_btn.clicked.connect(self.start_service)
        self.stop_btn.clicked.connect(self.stop_service)
        
        layout.addWidget(self.status_label)
        layout.addWidget(self.start_btn)
        layout.addWidget(self.stop_btn)
        
    def _create_window_controls(self, layout):
        """创建窗口控制按钮"""
        btn_minimize = self.create_control_button("—")
        btn_close = self.create_control_button("×")
        btn_minimize.clicked.connect(self.minimize_requested.emit)
        btn_close.clicked.connect(self.close_requested.emit)
        
        layout.addWidget(btn_minimize)
        layout.addWidget(btn_close)
        
    def create_control_button(self, text):
        """创建标题栏控制按钮"""
        btn = QPushButton(text)
        btn.setFixedSize(30, 30)
        btn.setStyleSheet(UIStyles.CONTROL_BUTTON_STYLE)
        return btn
        
    def start_service(self):
        """启动服务"""
        if not self.start_btn.isEnabled():
            return
            
        try:
            # 禁用按钮防止重复点击
            self.start_btn.setEnabled(False)
            self.stop_btn.setEnabled(False)
            
            # 设置加载状态样式
            self.start_btn.setStyleSheet("""
            QPushButton {
                color: #606060;
                background-color: rgba(37, 117, 252, 0.1);
                border: 1px solid #606060;
                border-radius: 4px;
                padding: 6px 15px;
                font-size: 12px;
                min-width: 100px;
            }
            """)
            self.status_label.setText("启动中...")
            self.status_label.setStyleSheet("""
                QLabel {
                    color: #FFA500;
                    font-size: 14px;
                    font-weight: bold;
                    min-width: 80px;
                    text-align: center;
                }
            """)
            
            # 发射启动信号
            self.start_service_requested.emit()
            
        except Exception as e:
            self.reset_buttons()
            
    def stop_service(self):
        """停止服务"""
        if not self.stop_btn.isEnabled():
            return
            
        try:
            # 创建赛博朋克风格确认对话框
            msg_box = QMessageBox()
            msg_box.setWindowTitle("确认停止服务")
            msg_box.setText("确定要停止服务吗？")
            msg_box.setStandardButtons(QMessageBox.Ok | QMessageBox.Cancel)
            msg_box.setDefaultButton(QMessageBox.Cancel)
            
            # 设置对话框显示在主窗口中央
            if self.main_window:
                def adjust_position():
                    center = self.main_window.geometry().center()
                    msg_box.move(center - msg_box.rect().center())
                
                # 显示后立即调整位置
                msg_box.show()
                adjust_position()
            
            # 设置赛博朋克样式
            msg_box.setStyleSheet(UIStyles.MESSAGE_BOX_STYLE)
            
            if msg_box.exec_() == QMessageBox.Ok:
                # 设置停止中状态
                self.stop_btn.setEnabled(False)
                self.stop_btn.setStyleSheet("""
                    QPushButton {
                        color: #808080;
                        background-color: rgba(106, 17, 203, 0.1);
                        border: 1px solid #808080;
                        border-radius: 4px;
                        padding: 6px 15px;
                        font-size: 12px;
                        min-width: 100px;
                    }
                """)
                self.status_label.setText("停止中...")
                self.status_label.setStyleSheet("""
                    QLabel {
                        color: #FFA500;
                        font-size: 14px;
                        font-weight: bold;
                        min-width: 80px;
                        text-align: center;
                    }
                """)
                
                # 发射停止信号
                self.stop_service_requested.emit()
                
        except Exception as e:
            self.reset_buttons()
            
    def reset_buttons(self):
        """重置按钮状态"""
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        
        # 启动按钮样式
        self.start_btn.setStyleSheet(UIStyles.START_BUTTON_STYLE)
        
        # 停止按钮禁用状态
        self.stop_btn.setStyleSheet("""
            QPushButton {
                color: #606060;
                background-color: rgba(106, 17, 203, 0.1);
                border: 1px solid #606060;
                border-radius: 4px;
                padding: 6px 15px;
                font-size: 12px;
                min-width: 100px;
            }
        """)
        
        # 状态标签
        self.status_label.setText("服务未启动")
        self.status_label.setStyleSheet(UIStyles.STATUS_LABEL_STYLE)
        
    def update_service_status(self, message, ip=None, port=None):
        """更新服务状态"""
        if "服务启动" in message:
            # 启动成功后启用停止按钮
            self.stop_btn.setEnabled(True)
            self.stop_btn.setStyleSheet(UIStyles.STOP_BUTTON_STYLE)
            
            status_text = f"服务已启动 {ip}:{port}" if ip and port else "服务已启动"
            self.status_label.setText(status_text)
            self.status_label.setStyleSheet("""
                QLabel {
                    color: #00FF00;
                    font-size: 14px;
                    font-weight: bold;
                    min-width: 80px;
                    text-align: center;
                }
            """)
        elif "服务关闭" in message:
            # 停止服务后重置按钮状态
            self.reset_buttons()
