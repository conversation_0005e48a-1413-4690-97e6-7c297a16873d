from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QTabWidget)
from PyQt5.QtCore import Qt
from .区服配置 import ServerConfigPage
from .角色配置 import CharacterConfigPage
from .任务配置 import TaskConfigPage

class ScriptConfigPage(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()

    def setup_ui(self):
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        
        # 创建导航栏
        self.create_nav_bar(main_layout)
        
        # 移除堆叠窗口，直接使用标签页内容

    def create_nav_bar(self, layout):
        """创建标签页导航栏"""
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: none;
            }
            QTabBar::tab {
                color: #00F5FF;
                background: rgba(20, 25, 45, 0.5);
                padding: 8px 15px;
                border: 1px solid rgba(0, 245, 255, 0.2);
                border-bottom: none;
                border-top-left-radius: 5px;
                border-top-right-radius: 5px;
            }
            QTabBar::tab:selected {
                background: rgba(106, 17, 203, 0.3);
                border-color: rgba(0, 245, 255, 0.5);
            }
        """)
        
        # 添加标签页
        self.tab_widget.addTab(ServerConfigPage(self), "区服配置")
        self.tab_widget.addTab(CharacterConfigPage(self), "角色配置") 
        self.tab_widget.addTab(TaskConfigPage(self), "任务配置")
        
        layout.addWidget(self.tab_widget)
