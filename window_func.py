from PyQt5.QtWidgets import QTableWidgetItem
from PyQt5.QtGui import QColor
from PyQt5.QtCore import QObject, pyqtSignal, QMetaObject, Qt, Q_ARG, QTimer
from datetime import datetime
from pathlib import Path
import sqlite3
import time
from queue import Queue
from typing import Optional, Dict, Any, List, Tuple

import threading

class SQLiteConnectionPool:
    _instance = None
    _local = threading.local()
    
    def __new__(cls, db_path: str, pool_size: int = 5):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance.db_path = db_path
            cls._instance.pool_size = pool_size
        return cls._instance
    
    def _init_pool(self):
        if not hasattr(self._local, 'pool'):
            self._local.pool = Queue(self.pool_size)
            for _ in range(self.pool_size):
                conn = sqlite3.connect(self.db_path)
                # 启用WAL模式
                conn.execute("PRAGMA journal_mode=WAL")
                conn.execute("PRAGMA synchronous=NORMAL")
                conn.execute("PRAGMA busy_timeout=3000")  # 3秒超时
                self._local.pool.put(conn)
    
    def get_connection(self) -> sqlite3.Connection:
        self._init_pool()
        return self._local.pool.get()
    
    def return_connection(self, conn: sqlite3.Connection):
        self._init_pool()
        self._local.pool.put(conn)

# 定义更新信号
class DeviceSignals(QObject):
    """设备信号管理类"""
    update_action_signal = pyqtSignal(str, str)  # device_id, action
    update_mode_signal = pyqtSignal(str, str)  # device_id, mode
    update_account_signal = pyqtSignal(str, str)  # device_id, account
    update_map_signal = pyqtSignal(str, str)  # device_id, map_name
    update_character_signal = pyqtSignal(str, str)  # device_id, character_name
    update_level_signal = pyqtSignal(str, int)  # device_id, level

device_signals = DeviceSignals()

_main_window = None

def set_main_window(window) -> None:
    """设置主窗口实例引用"""
    global _main_window
    _main_window = window
    
 # =========================================日志下============================================   
class 日志:
    """日志管理类 - 负责操作日志的打印和管理"""

    @staticmethod
    def log_message(message: str, typ: str = "信息", show_time: bool = True) -> None:
        """
        在操作日志窗口打印消息

        Args:
            message: 要打印的消息内容
            typ: 消息类型，默认为"信息"
            show_time: 是否显示时间，默认为True
        """
        if _main_window is not None and hasattr(_main_window, 'append_output'):
            try:
                if show_time:
                    current_time = datetime.now().strftime("%H:%M:%S")
                    message = f"[{current_time}] {message}"
                # 简单直接的字符串传递
                _main_window.append_output(str(typ), str(message))
            except Exception:
                # 静默失败，不影响主要功能
                pass
 # =========================================日志上============================================   

# =========================================连接管理============================================
class 连接管理:
    """连接管理类 - 负责设备连接状态的管理和表格更新"""

    # 设备行缓存，用于优化表格查找性能
    _device_row_cache: Dict[str, int] = {}

    @staticmethod
    def add_connection_row(phone_id: str, phone_ip: str,
                          group: str, number: str, status: str = "已连接") -> None:
        """
        向连接管理窗口表格添加一行信息

        Args:
            phone_id: 手机ID
            phone_ip: 手机IP
            group: 分组
            number: 编号
            status: 连接状态，默认为"已连接"
        """
        if _main_window is None:
            return

        device_info = {
            "device_id": phone_id,
            "ip": phone_ip,
            "group": group,
            "identifier": number,
            "status": status
        }
        if hasattr(_main_window, 'update_device_table'):
            _main_window.update_device_table(device_info)

            # 更新账号管理中的登录状态
            账号管理.update_account_login_status_by_device(phone_id)

            # 通过设备ID更新连接账号信息
            if hasattr(_main_window, 'account_page'):
                连接管理.update_connection_account(phone_id)
    @staticmethod
    def delete_row_by_ip(ip: str) -> bool:
        """
        通过IP删除表格数据

        Args:
            ip: 要删除的设备IP

        Returns:
            bool: 删除成功返回True，否则返回False
        """
        if _main_window is None or not hasattr(_main_window, 'connection_page'):
            日志.log_message(f"delete_row_by_ip: 主窗口或连接管理页面未初始化", "错误")
            return False

        table = _main_window.connection_page.phone_table
        for row in range(table.rowCount()-1, -1, -1):  # 反向遍历避免索引问题
            item = table.item(row, 1)  # 第1列是IP
            if not item:
                continue

            current_ip = item.text()
            if current_ip == ip:  # 精确匹配
                # 清除缓存中对应的设备
                device_item = table.item(row, 0)
                if device_item:
                    device_id = device_item.text()
                    连接管理._device_row_cache.pop(device_id, None)

                table.removeRow(row)
                日志.log_message(f"{ip} 已断开连接", "警告")
                return True

                日志.log_message(f"未找到IP {ip} 对应的设备", "警告")
        return False

    @staticmethod
    def _find_device_row(device_id: str) -> int:
        """
        使用缓存机制快速查找设备行

        Args:
            device_id: 设备ID

        Returns:
            int: 设备所在行号，未找到返回-1
        """
        if not _main_window or not hasattr(_main_window, 'connection_page'):
            return -1

        table = _main_window.connection_page.phone_table

        # 首先检查缓存
        if device_id in 连接管理._device_row_cache:
            cached_row = 连接管理._device_row_cache[device_id]
            # 验证缓存是否有效
            if (cached_row < table.rowCount() and
                table.item(cached_row, 0) and
                table.item(cached_row, 0).text() == device_id):
                return cached_row
            else:
                # 缓存失效，移除
                连接管理._device_row_cache.pop(device_id, None)

        # 缓存失效或不存在，重新查找
        for row in range(table.rowCount()):
            item = table.item(row, 0)  # 第0列是设备ID
            if item and item.text() == device_id:
                # 更新缓存
                连接管理._device_row_cache[device_id] = row
                return row

        return -1

    @staticmethod
    def update_device_action_by_id(device_id: str, action: str) -> bool:
        """
        通过设备ID更新表格中的当前动作

        Args:
            device_id: 设备ID
            action: 要更新的动作内容

        Returns:
            bool: 更新成功返回True，否则返回False
        """
        if _main_window is None or not hasattr(_main_window, 'connection_page'):
            日志.log_message("update_device_action_by_id: 主窗口或连接管理页面未初始化", "错误")
            return False

        # 使用信号触发更新，确保线程安全
        device_signals.update_action_signal.emit(device_id, action)
        return True

    @staticmethod
    def update_device_mode_by_id(device_id: str, mode: str) -> bool:
        """
        通过设备ID更新表格中的操作模式

        Args:
            device_id: 设备ID
            mode: 要更新的操作模式

        Returns:
            bool: 更新成功返回True，否则返回False
        """
        if _main_window is None or not hasattr(_main_window, 'connection_page'):
            日志.log_message("update_device_mode_by_id: 主窗口或连接管理页面未初始化", "错误")
            return False

        # 使用信号触发更新，确保线程安全
        device_signals.update_mode_signal.emit(device_id, mode)
        return True
    @staticmethod
    def update_device_server_by_id(device_id: str, server: str):
        """
        通过设备ID更新表格中的区服信息
        参数:
            device_id: 设备ID(必需)
            server: 要更新的区服名称(必需)
        """
        if _main_window is None or not hasattr(_main_window, 'connection_page'):
            日志.log_message("update_device_server_by_id: 主窗口或连接管理页面未初始化", "错误")
            return False
            
        # 查找设备所在行
        table = _main_window.connection_page.phone_table
        for row in range(table.rowCount()):
            item = table.item(row, 0)  # 第0列是设备ID
            if item and item.text() == device_id:
                # 确保更新的是区服列(第5列)
                if table.columnCount() > 5:
                    server_item = QTableWidgetItem(server)
                    table.setItem(row, 5, server_item)
                    日志.log_message(f"{device_id} 区服更新为: {server}", "信息")
                    return True
                else:
                    日志.log_message(f"表格列数不足，无法更新区服信息", "错误")
                    return False
                
        日志.log_message(f"未找到设备 {device_id}", "警告")
        return False
    @staticmethod
    def update_device_map(device_id: str, map_name: str):
        """
        更新连接管理中的当前地图信息
        参数:
            device_id: 手机ID(必需)
            map_name: 地图名称(必需)
        """
        if _main_window is None or not hasattr(_main_window, 'connection_page'):
            日志.log_message("update_device_map: 主窗口或连接管理页面未初始化", "错误")
            return False

        # 使用信号触发更新，确保线程安全
        device_signals.update_map_signal.emit(device_id, map_name)
        
        table = _main_window.connection_page.phone_table
        日志.log_message(f"开始更新设备 {device_id} 的地图信息为 {map_name}", "调试")
        日志.log_message(f"表格总行数: {table.rowCount()}, 总列数: {table.columnCount()}", "调试")
        
        for row in range(table.rowCount()):
            item = table.item(row, 0)  # 第0列是设备ID
            if item and item.text() == device_id:
                日志.log_message(f"找到匹配设备 {device_id} 在第 {row} 行", "调试")
                # 确保更新的是当前地图列(第6列)
                if table.columnCount() > 6:
                    map_item = QTableWidgetItem(map_name)
                    table.setItem(row, 6, map_item)
                    # 强制刷新表格视图
                    table.viewport().update()
                    日志.log_message(f"{device_id} 当前地图更新为: {map_name}", "信息")
                    return True
                else:
                    日志.log_message(f"表格列数不足，无法更新地图信息(当前列数: {table.columnCount()})", "错误")
                    return False
                
        日志.log_message(f"未找到设备 {device_id}，当前表格内容: {[table.item(row, 0).text() for row in range(table.rowCount()) if table.item(row, 0)]}", "警告")
        return False
    @staticmethod
    def update_device_character(device_id: str, character_name: str):
        """
        更新连接管理中的角色名信息
        参数:
            device_id: 手机ID(必需)
            character_name: 角色名称(必需)
        """
        if _main_window is None or not hasattr(_main_window, 'connection_page'):
            日志.log_message("update_device_character: 主窗口或连接管理页面未初始化", "错误")
            return False

        # 使用信号触发更新，确保线程安全
        device_signals.update_character_signal.emit(device_id, character_name)
        
        table = _main_window.connection_page.phone_table
        for row in range(table.rowCount()):
            item = table.item(row, 0)  # 第0列是设备ID
            if item and item.text() == device_id:
                # 确保更新的是角色名列(第7列)
                if table.columnCount() > 7:
                    character_item = QTableWidgetItem(character_name)
                    table.setItem(row, 7, character_item)
                    日志.log_message(f"{device_id} 角色名更新为: {character_name}", "信息")
                    return True
                else:
                    日志.log_message(f"表格列数不足，无法更新角色名信息", "错误")
                    return False
                
        日志.log_message(f"未找到设备 {device_id}", "警告")
        return False
    @staticmethod
    def update_device_level(device_id: str, level: int):
        """
        更新连接管理中的角色等级信息
        参数:
            device_id: 手机ID(必需)
            level: 角色等级(必需)
        """
        if _main_window is None or not hasattr(_main_window, 'connection_page'):
            日志.log_message("update_device_level: 主窗口或连接管理页面未初始化", "错误")
            return False

        # 使用信号触发更新，确保线程安全
        device_signals.update_level_signal.emit(device_id, level)
        
        table = _main_window.connection_page.phone_table
        for row in range(table.rowCount()):
            item = table.item(row, 0)  # 第0列是设备ID
            if item and item.text() == device_id:
                # 确保更新的是等级列(第8列)
                if table.columnCount() > 8:
                    level_item = QTableWidgetItem(str(level))
                    table.setItem(row, 8, level_item)
                    日志.log_message(f"{device_id} 角色等级更新为: {level}", "信息")
                    return True
                else:
                    日志.log_message(f"表格列数不足，无法更新等级信息", "错误")
                    return False
                
        日志.log_message(f"未找到设备 {device_id}", "警告")
        return False

    @staticmethod
    def update_device_account(device_id: str, account: str):
        """
        更新连接管理中的登录账号信息
        参数:
            device_id: 手机ID(必需)
            account: 登录账号(必需)
        """
        if _main_window is None or not hasattr(_main_window, 'connection_page'):
            日志.log_message("update_device_account: 主窗口或连接管理页面未初始化", "错误")
            return False

        日志.log_message(f"开始更新设备账号 - 设备ID: {device_id}, 账号: {account}", "调试")

    @staticmethod
    def update_connection_account(device_id: str):
        """
        通过设备ID更新连接管理中的账号信息
        参数:
            device_id: 关联设备ID
        """
        if _main_window is None:
            return
            
        if hasattr(_main_window, 'account_page'):
            try:
                # 通过设备ID获取关联账号
                account = _main_window.account_page.get_account_by_device_id(device_id)
                if account:
                    连接管理.update_device_account(device_id, account)
                else:
                    日志.log_message(f"未找到设备 {device_id} 关联的账号", "警告")
            except Exception as e:
                日志.log_message(f"更新连接管理账号失败: {str(e)}", "错误")
        
        table = _main_window.connection_page.phone_table
        日志.log_message(f"表格总行数: {table.rowCount()}", "调试")
        
        found = False
        for row in range(table.rowCount()):
            item = table.item(row, 0)  # 第0列是设备ID
            if not item:
                日志.log_message(f"第{row}行没有设备ID", "调试")
                continue
                
            current_id = item.text()
            日志.log_message(f"检查第{row}行 - 设备ID: {current_id}", "调试")
            
            if current_id == device_id:
                日志.log_message(f"找到匹配设备 {device_id} 在第 {row} 行", "调试")
                found = True
                # 确保更新的是账号列(第4列)
                if table.columnCount() > 4:
                    account_item = QTableWidgetItem(account)
                    account_item.setTextAlignment(Qt.AlignCenter)
                    table.setItem(row, 4, account_item)
                    日志.log_message(f"{device_id} 登录账号更新为: {account}", "信息")
                    # 强制刷新表格
                    table.viewport().update()
                    return True
                else:
                    日志.log_message(f"表格列数不足，无法更新账号信息(当前列数: {table.columnCount()})", "错误")
                    return False
                
        if not found:
            日志.log_message(f"未找到设备 {device_id}, 当前表格设备ID: {[table.item(row,0).text() for row in range(table.rowCount()) if table.item(row,0)]}", "警告")
        return False



# =========================================账号管理============================================
class 账号管理:
    """账号管理类 - 负责游戏账号信息的管理和状态更新"""
    _db_pool = SQLiteConnectionPool(
        "d:/anzhuang/Aibote/Project/python_demo/Aibote_server/DB/account.db"
    )

    @staticmethod
    def _execute_with_retry(query: str, params: Tuple, max_retries: int = 3) -> Any:
        conn = None
        for attempt in range(max_retries):
            try:
                conn = 账号管理._db_pool.get_connection()
                cursor = conn.cursor()
                cursor.execute(query, params)
                conn.commit()
                return cursor
            except sqlite3.OperationalError as e:
                if "locked" in str(e) and attempt < max_retries - 1:
                    time.sleep(0.1 * (attempt + 1))
                    continue
                raise
            finally:
                if conn:
                    账号管理._db_pool.return_connection(conn)

    @staticmethod
    def update_account_login_status_by_device(device_id: str, status: str = "登录中") -> bool:
        """
        通过设备ID更新账号管理中的登录状态

        Args:
            device_id: 手机ID
            status: 要设置的状态，默认为"登录中"

        Returns:
            bool: 更新成功返回True，否则返回False
        """
        if _main_window is None or not hasattr(_main_window, 'account_page'):
            账号管理.log_message("update_account_login_status_by_device: 主窗口或账号管理页面未初始化", "错误")
            return False

            账号管理.log_message(f"开始通过设备ID更新账号状态: {device_id} -> {status}", "调试")

        try:
            # 通过手机ID查找对应账号
            for row in range(_main_window.account_page.table.rowCount()):
                device_id_item = _main_window.account_page.table.item(row, 2)  # 第2列是手机ID
                if device_id_item and device_id_item.text() == device_id:
                    account_item = _main_window.account_page.table.item(row, 0)  # 第0列是账号
                    if account_item:
                        _main_window.account_page.update_login_status(account_item.text(), status)
                        日志.log_message(f"账号 {account_item.text()} 状态更新为: {status}", "信息")
                        return True

            账号管理.log_message(f"未找到设备 {device_id} 对应的账号", "警告")
            return False
        except Exception as e:
            日志.log_message(f"更新账号登录状态失败: {str(e)}", "错误")
            return False
            
    @staticmethod
    def update_account_server_by_device(device_id: str, server_name: str):
        """
        通过设备ID更新账号的登录区服
        参数:
            device_id: 手机ID
            server_name: 要设置的区服名称
        """
        if _main_window is None or not hasattr(_main_window, 'account_page'):
            日志.log_message("update_account_server_by_device: 主窗口或账号管理页面未初始化", "错误")
            return False
            
        日志.log_message(f"开始通过设备ID更新账号区服: {device_id} -> {server_name}", "调试")
        
        try:
            # 通过手机ID查找对应账号
            for row in range(_main_window.account_page.table.rowCount()):
                device_id_item = _main_window.account_page.table.item(row, 2)  # 第2列是手机ID
                if device_id_item and device_id_item.text() == device_id:
                    account_item = _main_window.account_page.table.item(row, 0)  # 第0列是账号
                    if account_item:
                        try:
                            # 使用连接池更新数据库
                            账号管理._execute_with_retry(
                                "UPDATE 账号表 SET 登录区服=? WHERE 设备ID=?",
                                (server_name, device_id)
                            )
                            
                            # 更新UI表格中的区服列(假设第3列是区服)
                            if _main_window.account_page.table.columnCount() > 3:
                                server_item = QTableWidgetItem(server_name)
                                server_item.setTextAlignment(Qt.AlignCenter)
                                _main_window.account_page.table.setItem(row, 3, server_item)
                                _main_window.account_page.table.viewport().update()
                                
                            日志.log_message(f"账号 {account_item.text()} 区服更新为: {server_name}", "信息")
                            return True
                        except sqlite3.Error as e:
                            日志.log_message(f"数据库操作失败: {str(e)}", "错误")
                            return False
                        
            日志.log_message(f"未找到设备 {device_id} 对应的账号", "警告")
            return False
        except Exception as e:
            日志.log_message(f"更新账号区服失败: {str(e)}", "错误")
            return False
            
    @staticmethod
    def 查询并更新连接管理区服(device_id: str) -> str:
        """
        查询账号表的登录区服并更新连接管理显示
        参数:
            device_id: 设备ID
        返回:
            登录区服名称(如果有), 否则返回空字符串
        """
        if _main_window is None or not hasattr(_main_window, 'account_page'):
            日志.log_message("查询并更新连接管理区服: 主窗口或账号管理页面未初始化", "错误")
            return ""
            
        日志.log_message(f"开始查询设备 {device_id} 的登录区服", "调试")
        
        try:
            # 使用连接池查询数据库
            cursor = 账号管理._execute_with_retry(
                "SELECT 登录区服 FROM 账号表 WHERE 设备ID=?",
                (device_id,)
            )
            result = cursor.fetchone()
            
            if result and result[0]:  # 有登录区服数据
                登录区服 = result[0]
                # 更新连接管理显示
                连接管理.update_device_server_by_id(device_id, 登录区服)
                日志.log_message(f"设备 {device_id} 登录区服更新为: {登录区服}", "信息")
                return 登录区服
            else:
                日志.log_message(f"设备 {device_id} 无登录区服记录", "警告")
                return ""
                
        except sqlite3.Error as e:
            日志.log_message(f"查询登录区服失败: {str(e)}", "错误")
            return ""
    @staticmethod
    def update_account_remark_by_device(device_id: str, remark: str):
        """
        通过设备ID更新账号表的备注信息
        参数:
            device_id: 手机ID(必需)
            remark: 要更新的备注内容(必需)
        返回:
            更新成功返回True，否则返回False
        """
        if _main_window is None or not hasattr(_main_window, 'account_page'):
            日志.log_message("update_account_remark_by_device: 主窗口或账号管理页面未初始化", "错误")
            return False
            
        日志.log_message(f"开始通过设备ID更新账号备注: {device_id} -> {remark}", "调试")
        
        try:
            # 使用连接池更新数据库
            cursor = 账号管理._execute_with_retry(
                "UPDATE 账号表 SET 备注=? WHERE 设备ID=?",
                (remark, device_id)
            )
            
            # 检查是否有行被更新
            if cursor.rowcount == 0:
                日志.log_message(f"未找到设备 {device_id} 对应的账号记录", "警告")
                return False
            
            # 更新UI表格中的备注列(第9列是备注)
            for row in range(_main_window.account_page.table.rowCount()):
                device_id_item = _main_window.account_page.table.item(row, 2)  # 第2列是手机ID
                if device_id_item and device_id_item.text() == device_id:
                    # 更新UI表格
                    if _main_window.account_page.table.columnCount() > 9:
                        remark_item = QTableWidgetItem(remark)
                        remark_item.setTextAlignment(Qt.AlignCenter)
                        _main_window.account_page.table.setItem(row, 9, remark_item)
                        _main_window.account_page.table.viewport().update()
                        日志.log_message(f"设备 {device_id} 的账号备注已更新为: {remark}", "信息")
                        return True
                    else:
                        日志.log_message(f"表格列数不足，无法更新备注信息", "错误")
                        return False
            
            日志.log_message(f"数据库更新成功，但UI表格中未找到设备 {device_id}", "警告")
            return True
            
        except sqlite3.Error as e:
            日志.log_message(f"更新账号备注失败: {str(e)}", "错误")
            return False
        except Exception as e:
            账号管理.log_message(f"更新账号备注时发生异常: {str(e)}", "错误")
            return False
