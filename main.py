import sys
import warnings
warnings.filterwarnings("ignore", category=DeprecationWarning)
from PyQt5.QtCore import qInstallMessageHandler
import winreg

def qt_message_handler(mode, context, message):
    """过滤QTextCursor和QVector<int>相关的警告"""
    if "QTextCursor" not in message and "QVector<int>" not in message:
        print(message)

# 安装Qt消息处理器
qInstallMessageHandler(qt_message_handler)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt5.QtWidgets import QApplication, QTableWidgetItem
from UI import MainWindowUI
from PyAibote import AndroidBotMain,WinBotMain
import time, traceback, psutil
from Aibote import Android
import window_func
from window_func import 连接管理,日志,账号管理
from jx3_Android.业务检测 import 任务检测,游戏内检测
from jx3_Android.任务分类 import 任务分发


# 必须在创建QApplication前设置高DPI
QApplication.setAttribute(Qt.AA_EnableHighDpiScaling)

# 主窗口类，负责UI界面和线程管理,继承自MainWindowUI，提供完整的窗口功能
class CyberpunkMainWindow(MainWindowUI):

    def __init__(self):
        super().__init__()

        # 连接设备更新信号
        from window_func import device_signals
        device_signals.update_action_signal.connect(self.handle_update_action)
        device_signals.update_mode_signal.connect(self.handle_update_mode)

        # 初始化QTextCursor相关设置
        from PyQt5.QtGui import QTextCursor

        # 初始化业务组件
        self.init_business_components()

        # 最后设置主窗口引用
        window_func.set_main_window(self)

    def init_business_components(self):
        """初始化业务组件"""
        # 初始化后台线程
        self.init_thread()

        # 表格更新防抖机制改为通过线程信号处理
        self.pending_device_updates = {}

        # 连接UI信号到业务逻辑
        self.connect_ui_signals()

        # 初始化页面
        self.init_pages()

    def connect_ui_signals(self):
        """连接UI信号到业务逻辑"""
        # 连接服务控制信号
        self.start_service_requested.connect(self.handle_start_service)
        self.stop_service_requested.connect(self.handle_stop_service)
        self.page_switched.connect(self.handle_page_switch)

    def init_thread(self):
        """初始化后台线程"""
        self.thread = AndroidControlThread()
        self.thread.progress.connect(self.update_ui)

    def init_pages(self):
        """初始化各个页面"""
        # 创建连接管理页面(设备表格)
        self.create_phone_table_page()

        # 创建账号管理页面
        from UI.账号管理 import AccountManagementPage
        self.account_page = AccountManagementPage()
        self.add_page(self.account_page)

        # 创建脚本配置页面
        from UI.脚本配置 import ScriptConfigPage
        self.script_page = ScriptConfigPage(self)
        self.add_page(self.script_page)

        # 创建系统设置页面
        self.create_settings_page()

    def handle_start_service(self, default_ip, default_port):
        """处理启动服务请求"""
        try:
            # 获取服务器信息
            ip, port = default_ip, default_port
            if hasattr(self, 'settings_page'):
                ip, port = self.settings_page.get_server_info()

            self.append_output("系统", f"正在启动服务 {ip}:{port}...")

            # 更新线程配置并启动
            self.thread.set_server_info(ip, port)
            self.thread.start()

        except Exception as e:
            self.append_output("错误", f"启动服务失败: {str(e)}")
            self.reset_service_buttons()

    def handle_stop_service(self):
        """处理停止服务请求"""
        try:
            self.thread.stop_service()
        except Exception as e:
            self.append_output("错误", f"停止服务时发生错误: {str(e)}")
            self.reset_service_buttons()

    def handle_page_switch(self, page_name):
        """处理页面切换"""
        try:
            print(f"切换到页面: {page_name}")  # 控制台输出当前页面名称

            # 在切换前清除当前页面的选中状态
            current_widget = self.get_current_page()
            if hasattr(current_widget, 'clear_selection'):
                current_widget.clear_selection()

            # 隐藏账号管理浮窗
            if hasattr(self, 'account_page') and hasattr(self.account_page, 'current_server_ui'):
                if self.account_page.current_server_ui and self.account_page.current_server_ui.isVisible():
                    self.account_page.current_server_ui.setVisible(False)

            # 根据页面名称切换堆叠窗口的当前页
            if page_name == "连接管理":
                self.set_current_page(0)
            elif page_name == "账号管理":
                self.set_current_page(1)
            elif page_name == "脚本配置":
                self.set_current_page(2)
            elif page_name == "系统设置":
                self.set_current_page(3)

            self.append_output("系统", f"切换到 {page_name} 页面")
        except Exception as e:
            self.append_output("错误", f"切换页面时发生错误: {str(e)}")
    


    def create_phone_table_page(self):
        """创建连接管理页面(设备表格)"""
        from UI.连接管理 import ConnectionManagementPage
        self.connection_page = ConnectionManagementPage(self)
        self.add_page(self.connection_page)

    def create_settings_page(self):
        """创建系统设置页面"""
        from UI.系统设置 import SystemSettingsPage
        self.settings_page = SystemSettingsPage(self)
        self.add_page(self.settings_page)
    

    
    def update_ui(self, message):
        """
        更新UI的槽函数
        :param message: 接收到的消息文本
        """
        self.append_output("信息", message)

        # 根据消息内容更新UI状态
        if message == "update_table":
            # 处理表格更新信号
            if hasattr(self, 'connection_page'):
                # 获取当前连接设备并更新表格
                if hasattr(self.connection_page, 'phone_table'):
                    table = self.connection_page.phone_table
                    for row in range(table.rowCount()):
                        device_id = table.item(row, 0).text()  # 第0列是设备ID
                        device_info = {
                            "device_id": device_id,
                            "status": "已连接"  # 保持状态不变
                        }
                        self.connection_page.update_device_table(device_info)
        elif "服务启动" in message:
            # 启动成功后更新服务状态
            self.update_service_status(message, self.thread.ip, self.thread.port)
        elif "服务关闭" in message:
            # 停止服务后重置按钮状态
            self.update_service_status(message)
    
    def update_device_table(self, device_info):
        """更新设备表格(带防抖机制)"""
        try:
            # 为测试数据生成唯一ID
            if device_info["device_id"] == "测试ID":
                device_info["device_id"] = f"测试ID_{time.time()}"

            # 通过connection_page更新表格
            if hasattr(self, 'connection_page'):
                self.connection_page.update_device_table(device_info)
            else:
                self.append_output("错误", "连接管理页面未初始化")
        except Exception as e:
            self.append_output("错误", f"更新设备表格失败: {str(e)}")

    def handle_update_action(self, device_id: str, action: str):
        """处理设备动作更新信号"""
        try:
            if not hasattr(self, 'connection_page'):
                return

            table = self.connection_page.phone_table
            if table.columnCount() <= 9:  # 检查列数是否足够
                return

            for row in range(table.rowCount()):
                item = table.item(row, 0)  # 第0列是device_id
                if item and item.text() == device_id:
                    # 更新当前动作列(第9列)
                    action_item = QTableWidgetItem(action)
                    table.setItem(row, 9, action_item)
                    # 强制刷新表格
                    table.viewport().update()
                    日志.log_message(f"{device_id} 当前动作: {action}", "信息")
                    return

            日志.log_message(f"未找到设备ID {device_id} 对应的设备", "警告")

        except Exception as e:
            日志.log_message(f"handle_update_action错误: {str(e)}", "error")

    def handle_update_mode(self, device_id: str, mode: str):
        """处理设备操作模式更新信号"""
        try:
            if not hasattr(self, 'connection_page'):
                return

            table = self.connection_page.phone_table
            if table.columnCount() <= 10:  # 检查列数是否足够
                return

            for row in range(table.rowCount()):
                item = table.item(row, 0)  # 第0列是device_id
                if item and item.text() == device_id:
                    # 更新操作模式列(第10列)
                    mode_item = QTableWidgetItem(mode)
                    mode_item.setTextAlignment(Qt.AlignCenter)
                    table.setItem(row, 10, mode_item)
                    # 强制刷新表格
                    table.viewport().update()
                    日志.log_message(f"{device_id} 操作模式: {mode}", "信息")
                    return

            日志.log_message(f"未找到设备ID {device_id} 对应的设备", "警告")

        except Exception as e:
            日志.log_message(f"handle_update_mode错误: {str(e)}", "错误")

# PyQt5 后台线程类,继承自QThread，负责在后台运行Android服务        
class AndroidControlThread(QThread):
    """Android设备控制线程"""
    progress = pyqtSignal(str)  # 简化为直接发送消息文本

    def __init__(self):
        super().__init__()
        self._load_config()
        self.ip = "127.0.0.1"
        self.port = 8888
        self.stop_flag = False
        
        # 创建定时器用于表格更新防抖
        self.table_update_timer = QTimer()
        self.table_update_timer.setSingleShot(True)
        self.table_update_timer.timeout.connect(self._perform_table_update)

    def _load_config(self):
        """从注册表加载配置"""
        try:
            key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, r"Software\Aibote\ServerConfig")
            self.ip = winreg.QueryValueEx(key, "ServerIP")[0]
            self.port = int(winreg.QueryValueEx(key, "ServerPort")[0])
            winreg.CloseKey(key)
        except:
            self.ip, self.port = "127.0.0.1", 8888  # 默认值
    def run(self):
        """线程主运行方法"""
        try:
            self.progress.emit(f'服务启动成功 IP:{self.ip} 端口:{self.port}')
            # 启动定时器
            self.table_update_timer.start(100)
            # 在新线程中执行服务启动
            from threading import Thread
            Thread(target=CustomAndroidScript.execute, args=(self.ip, self.port), daemon=True).start()
        except Exception as e:
            self.progress.emit(f'服务启动失败: {str(e)}')

    def stop_service(self):
        """停止服务"""
        try:
            CustomAndroidScript.StopSrver()
            self.table_update_timer.stop()
            self.progress.emit('服务关闭成功')
        except Exception as e:
            self.progress.emit(f'服务关闭失败: {str(e)}')

    def _perform_table_update(self):
        """定时器触发时执行表格更新"""
        try:
            # 通过信号通知主线程更新表格
            self.progress.emit('update_table')
        except Exception as e:
            self.progress.emit(f'表格更新失败: {str(e)}')

    def set_server_info(self, ip, port):
        """设置服务器信息"""
        self.ip, self.port = ip, port

# 2. 自定义一个脚本类,Android设备控制脚本类,继承 AndroidBotMain,实现具体的设备操作逻辑
class CustomAndroidScript(AndroidBotMain):
    """Android设备控制脚本"""
    Log_Level = "INFO"    # 日志级别 INFO不打印
    Log_Storage = False     # 是否存储日志
    resolution_a = (2400, 1080)  # 抓取过坐标的设备标准分辨率
    # resolution_a = (1920, 1080)  # 抓取过坐标的设备标准分辨率
    _instances = {}  # 类变量保存所有实例 {device_id: instance}
    
    # 4. Hid底层是windows来操作的所以需要windows驱动
    WinDriving = WinBotMain._build("127.0.0.1", 7837, True)
    WinDriving.init_hid()
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.android_id = None #手机设备ID
        self.device_mode = None #手机操作类型HID和无障碍
        self.resolution_b = None #需要转换的设备分辨率
        self.登录区服 = None  # 登录区服
        self.业务标记 = None  # 初始化业务标记属性            
    def script_main(self):
        """主业务逻辑，由框架自动调用"""
        # 获取手机连接信息
        Android.get_Connection_information(self)
        self.登录区服 = 账号管理.查询并更新连接管理区服(self.android_id)
        #添加实例对象到类字典对象
        self.__class__._instances[self.android_id] = self 
        # print(f"实例对象: {self.__class__._instances.get(self.android_id)}")
        self.device_mode = Android.check_device_mode(self) #获取手机操作类型
        if self.device_mode:
            print(self.resolution_a)
            # print(self.resolution_b)          
            # for i in range(99):
            #     # get =Android.找色_单击(self,"#000000",[[16, 10, "#000000"], [16, 16, "#000000"], [24, 20, "#000000"]],(2094, 21, 2177, 65),1)
            #     get = 游戏内检测.进入游戏检测(self)
            #     # get = self._parsing_ocr_data((1873, 71, 1950, 127),(0,0,0),1)
            #     print(get)
            #     time.sleep(3)
            # 手机连接进来检测需要做什么任务
            # for i in range(100):
            #     get = self.get_text((0,0,0,0), (0,0,0), 1)
            #     print(get)
            #     time.sleep(1)
            # # #=========================截图==================
            # # # 截图坐标
            # result = self.take_screenshot((0, 0, 0, 0), (0,0,0), 1)
            # print(result) 
            # # 将字节流保存为图片使用示例 [Demo]
            # from PIL import Image
            # import io
            # image = Image.open(io.BytesIO(result))
            # image.save(r'C:\Users\<USER>\Desktop\1.png')
            # print("完成")
            # time.sleep(5)
            # result = self.get_image_size(r'C:\Users\<USER>\Desktop\1.png')  # 读取根目录中1.png图片大小
            # print(result)
            # time.sleep(10)
            # # #=========================截图==================
            # 任务检测(self)
            # self.业务标记 = 12
            # while True:
            for i in range(********):
                try:
                    # 任务分发(self)
                    result1 = self.app_is_running("微啊信")
                    print(result1)
                    time.sleep(0.3)
                    连接管理.update_device_action_by_id(self.android_id,str(i))
                    # 账号管理.update_account_remark_by_device(self.android_id,str(i))
                except OSError as e:
                    print(f"连接断开: {e}")
                    ip = str(e).split(":")[0]
                    连接管理.delete_row_by_ip(ip)#删除连接数据
                    日志.log_message(f"ID: {self.android_id}客户端手动断开连接","警告")
                    账号管理.update_account_login_status_by_device(self.android_id,"手动断开") #将账号管理设置
                    break
                except Exception as e:
                    ip = str(e).split(":")[0]
                    连接管理.delete_row_by_ip(ip) #删除连接数据
                    账号管理.update_account_login_status_by_device(self.android_id,"异常断开")
                    日志.log_message(f"ID: {self.android_id} 客户端异常断开连接", "警告")
                    break
        else:
            日志.log_message(f"ID: {self.android_id}设置操作类型无法获取关闭连接","警告")
            if self.close_driver():
                日志.log_message(f"ID: {self.android_id}无法获取操作模式关闭手机连接成功","信息")
            else:
                日志.log_message(f"ID: {self.android_id}无法获取操作模式关闭手机连接失败","警告")                         

# 程序入口
# 1. 创建一个主函数，负责启动应用程序
if __name__ == "__main__":
    
    app = QApplication(sys.argv)
    app.setStyle("Fusion")
    
    # 使用更兼容的方式注册元类型
    try:
        # 通过信号连接自动注册类型
        app.aboutToQuit.connect(lambda: None)  # 触发元类型系统初始化
    except Exception as e:
        print(f"初始化元类型系统时出错: {str(e)}")
    
    # 高DPI设置(已在文件开头设置)
    QApplication.setHighDpiScaleFactorRoundingPolicy(
        Qt.HighDpiScaleFactorRoundingPolicy.PassThrough
    )
    
    try:
        window = CyberpunkMainWindow()
        window.show()
        sys.exit(app.exec_())
    except Exception as e:
        print(f"Error: {str(e)}")
        print(traceback.format_exc())
