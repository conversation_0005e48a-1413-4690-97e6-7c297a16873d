import sys
import warnings
warnings.filterwarnings("ignore", category=DeprecationWarning)
from PyQt5.QtCore import qInstallMessageHandler, QtMsgType
import winreg

def qt_message_handler(mode, context, message):
    """过滤QTextCursor和QVector<int>相关的警告"""
    if "QTextCursor" not in message and "QVector<int>" not in message:
        print(message)

# 安装Qt消息处理器
qInstallMessageHandler(qt_message_handler)
from datetime import datetime
from PyQt5.QtCore import Qt, QPoint, QThread, pyqtSignal, QTimer, QRegExp
from PyQt5.QtGui import QColor, QFont, QTextCursor, QRegExpValidator, QIntValidator
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                            QHBoxLayout, QPushButton, QLabel, QFrame, QTextEdit,
                            Q<PERSON>plitter, QSizePolicy, QComboBox, QProgressBar,
                            QTableWidget, QTableWidgetItem, QHeaderView, QLineEdit,
                            QStackedWidget, QMessageBox, QDialog, QFileDialog,
                            QScrollArea)
from UI.系统监控 import SystemMonitor
from PyAibote import AndroidBotMain,WinBotMain
import time, traceback, psutil
from Aibote import Android
import window_func
from window_func import 连接管理,日志,账号管理
from jx3_Android.业务检测 import 任务检测,游戏内检测
from jx3_Android.任务分类 import 任务分发


# 必须在创建QApplication前设置高DPI
QApplication.setAttribute(Qt.AA_EnableHighDpiScaling)

# 主窗口类，负责UI界面和线程管理,继承自QMainWindow，提供完整的窗口功能
class CyberpunkMainWindow(QMainWindow):
    # 定义服务控制信号
    startService = pyqtSignal(str, int)  # ip, port
    stopService = pyqtSignal()
    
    def __init__(self):
        super().__init__()
        self.setWindowFlags(Qt.FramelessWindowHint)
        self.setAttribute(Qt.WA_TranslucentBackground)
        
        # 连接设备更新信号
        from window_func import device_signals
        device_signals.update_action_signal.connect(self.handle_update_action)
        device_signals.update_mode_signal.connect(self.handle_update_mode)
        
        # 初始化QTextCursor相关设置
        from PyQt5.QtGui import QTextCursor
        
        # 主窗口尺寸
        self.setFixedSize(1000, 600)

        # 先初始化所有UI组件
        self.init_ui_components()
        
        # 最后设置主窗口引用
        window_func.set_main_window(self)

    def init_ui_components(self):
        """初始化所有UI组件"""
        # 创建主容器和主布局
        self.main_container = QWidget()
        self.main_container.setObjectName("MainContainer")
        self.setCentralWidget(self.main_container)
        
        self.main_layout = QVBoxLayout(self.main_container)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(0)
        
        # 创建标题栏（包含服务控制按钮）
        self.create_title_bar()
        
        # 创建主内容区域
        self.create_main_content()
        
        # 初始化鼠标拖动相关变量
        self._is_dragging = False
        self._drag_start_position = None
        
        # 初始化后台线程
        self.init_thread()

        # 表格更新防抖机制改为通过线程信号处理
        self.pending_device_updates = {}
        
        # 设置基础样式
        self.setup_styles()

    def init_thread(self):
        """初始化后台线程"""
        self.thread = AndroidControlThread()
        self.thread.progress.connect(self.update_ui)
        # 连接服务控制信号
        self.startService.connect(self.thread.run)
        self.stopService.connect(self.thread.stop_service)

    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.LeftButton:
            self._is_dragging = True
            self._drag_start_position = event.globalPos() - self.frameGeometry().topLeft()
            event.accept()

    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        if self._is_dragging and event.buttons() == Qt.LeftButton:
            self.move(event.globalPos() - self._drag_start_position)
            event.accept()
            
            # 隐藏账号管理浮窗
            if hasattr(self, 'account_page') and hasattr(self.account_page, 'current_server_ui'):
                if self.account_page.current_server_ui and self.account_page.current_server_ui.isVisible():
                    self.account_page.current_server_ui.setVisible(False)

    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if event.button() == Qt.LeftButton:
            self._is_dragging = False
            event.accept()
    
    # 样式常量
    MAIN_CONTAINER_STYLE = """
        #MainContainer {
            background-color: rgba(20, 25, 45, 0.7);
            border-radius: 12px;
            border: 1px solid rgba(0, 245, 255, 0.3);
        }
    """
    
    # 四级字体系统
    TITLE_FONT = QFont("Segoe UI Semibold", 12)
    SUBTITLE_FONT = QFont("Segoe UI", 10)
    BODY_FONT = QFont("Segoe UI", 9)
    CODE_FONT = QFont("Consolas", 9)

    def setup_styles(self):
        """设置赛博朋克基础样式"""
        self.main_container.setStyleSheet(self.MAIN_CONTAINER_STYLE)
        QApplication.setFont(self.BODY_FONT)
    
    def create_title_bar(self):
        """创建自定义标题栏（包含服务控制按钮）"""
        title_bar = QWidget()
        title_bar.setFixedHeight(50)
        title_bar.setStyleSheet("background-color: transparent;")
        
        title_layout = QHBoxLayout(title_bar)
        title_layout.setContentsMargins(10, 0, 10, 0)
        
        # 左侧标题
        self._create_title_label(title_layout)
        
        # 右侧服务控制区域
        self._create_service_controls(title_layout)
        
        # 窗口控制按钮
        self._create_window_controls(title_layout)
        
        self.main_layout.addWidget(title_bar)
    
    def _create_title_label(self, layout):
        """创建标题标签"""
        title_label = QLabel("Aibote服务端控制中心")
        title_label.setStyleSheet("""
            QLabel {
                color: #00F5FF;
                font-family: "Segoe UI Semibold";
                font-size: 14pt;
                font-weight: 600;
                letter-spacing: 1px;
                padding-bottom: 2px;
            }
        """)
        layout.addWidget(title_label)
    
    def _create_service_controls(self, layout):
        """创建服务控制区域"""
        control_container = QWidget()
        control_layout = QHBoxLayout(control_container)
        control_layout.setContentsMargins(0, 0, 0, 0)
        control_layout.setSpacing(15)
        
        # 服务状态显示
        self.status_label = QLabel("服务未启动")
        self.status_label.setStyleSheet("""
            QLabel {
                color: #FF5555;
                font-family: "Segoe UI";
                font-size: 11pt;
                font-weight: 600;
                letter-spacing: 1px;
                min-width: 80px;
                text-align: center;
                text-transform: uppercase;
            }
        """)
        
        # 创建服务控制按钮
        self._create_service_buttons(control_layout)
        
        layout.addStretch()
        layout.addWidget(control_container)
    
    def _create_service_buttons(self, layout):
        """创建服务控制按钮"""
        self.start_btn = QPushButton("▶ 启动服务")
        self.stop_btn = QPushButton("■ 关闭服务")
        self.stop_btn.setEnabled(False)
        
        # 设置按钮样式
        start_btn_style = """
            QPushButton {
                color: #00FF00;
                background-color: rgba(37, 117, 252, 0.3);
                border: 1px solid #00FF00;
                border-radius: 4px;
                padding: 8px 12px;
                margin: 2px 5px;
                font-family: "Segoe UI";
                font-size: 9pt;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: rgba(0, 255, 0, 0.5);
            }
        """
        
        stop_btn_style = """
            QPushButton {
                color: #FF5555;
                background-color: rgba(37, 117, 252, 0.3);
                border: 1px solid #FF5555;
                border-radius: 4px;
                padding: 8px 12px;
                margin: 2px 5px;
                font-family: "Segoe UI";
                font-size: 9pt;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: rgba(255, 85, 85, 0.5);
            }
        """
        
        self.start_btn.setStyleSheet(start_btn_style)
        self.stop_btn.setStyleSheet(stop_btn_style)
        
        # 连接信号
        self.start_btn.clicked.connect(self.start_service)
        self.stop_btn.clicked.connect(self.stop_service)
        
        layout.addWidget(self.status_label)
        layout.addWidget(self.start_btn)
        layout.addWidget(self.stop_btn)
    
    def _create_window_controls(self, layout):
        """创建窗口控制按钮"""
        btn_minimize = self.create_control_button("—")
        btn_close = self.create_control_button("×")
        btn_minimize.clicked.connect(self.showMinimized)
        btn_close.clicked.connect(self.close)
        
        layout.addWidget(btn_minimize)
        layout.addWidget(btn_close)
    
    def create_control_button(self, text):
        """创建标题栏控制按钮"""
        btn = QPushButton(text)
        btn.setFixedSize(30, 30)
        btn.setStyleSheet("""
            QPushButton {
                color: #00F5FF;
                background-color: rgba(37, 117, 252, 0.3);
                border-radius: 15px;
                border: 1px solid rgba(0, 245, 255, 0.3);
                font-size: 16px;
            }
            QPushButton:hover {
                background-color: rgba(37, 117, 252, 0.5);
            }
            QPushButton:pressed {
                background-color: rgba(0, 245, 255, 0.7);
            }
        """)
        return btn
    
    def create_main_content(self):
        """创建主内容区域"""
        content_frame = QFrame()
        content_frame.setFrameShape(QFrame.NoFrame)
        content_frame.setStyleSheet("background-color: transparent;")
        
        self.content_layout = QHBoxLayout(content_frame)
        self.content_layout.setContentsMargins(0, 0, 0, 0)
        self.content_layout.setSpacing(5)
        
        # 左侧导航区
        self.create_navigation_panel()
        
        # 右侧内容区
        self.create_content_panel()
        
        self.main_layout.addWidget(content_frame)
    
    def create_navigation_panel(self):
        """创建左侧导航面板"""
        nav_frame = QFrame()
        nav_frame.setFixedWidth(130)
        nav_frame.setStyleSheet("""
            background-color: rgba(20, 25, 45, 0.5);
            border-radius: 8px;
            border: 1px solid rgba(0, 245, 255, 0.2);
        """)
        
        nav_layout = QVBoxLayout(nav_frame)
        nav_layout.setContentsMargins(0, 0, 0, 0)
        nav_layout.setSpacing(5)
        
        # 添加导航按钮
        nav_buttons = [
            ("连接管理", "📶"),
            ("账号管理", "👤"),
            ("脚本配置", "📜"),
            ("系统设置", "⚙️")
        ]
        
        self.nav_buttons = []
        for text, icon in nav_buttons:
            btn = self.create_nav_button(text, icon)
            self.nav_buttons.append(btn)
            nav_layout.addWidget(btn)
        
        # 添加Stretch确保监控区域固定在底部
        nav_layout.addStretch()
        
        # 添加系统监控组件
        self.system_monitor = SystemMonitor()
        nav_layout.addWidget(self.system_monitor)
        
        self.content_layout.addWidget(nav_frame)
    
    def create_nav_button(self, text, icon):
        """创建导航按钮"""
        btn = QPushButton(f"  {icon}   {text}")  # 增加图标和文本之间的间距
        btn.setFixedHeight(45)
        btn.setCheckable(True)
        btn.setStyleSheet("""
            QPushButton {
                color: #C0C0C0;
                background-color: transparent;
                text-align: left;
                padding-left: 10px;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                color: #00F5FF;
                background-color: rgba(106, 17, 203, 0.3);
            }
            QPushButton:pressed {
                background-color: rgba(37, 117, 252, 0.5);
            }
            QPushButton:checked {
                color: #00F5FF;
                background-color: rgba(37, 117, 252, 0.5);
                border-left: 3px solid #00F5FF;
            }
        """)
        
        # 绑定按钮点击事件
        btn.clicked.connect(lambda: self.switch_page(text, btn))
            
        return btn

    def switch_page(self, page_name, button=None):
        """切换内容页面"""
        try:
            print(f"切换到页面: {page_name}")  # 控制台输出当前页面名称
            
            # 更新按钮选中状态
            if button and hasattr(self, 'nav_buttons'):
                # 取消所有按钮的选中状态
                for btn in self.nav_buttons:
                    btn.setChecked(False)
                # 设置当前按钮为选中状态
                button.setChecked(True)
            
            # 在切换前清除当前页面的选中状态
            current_widget = self.stacked_widget.currentWidget()
            if hasattr(current_widget, 'clear_selection'):
                current_widget.clear_selection()
            
            # 隐藏账号管理浮窗
            if hasattr(self, 'account_page') and hasattr(self.account_page, 'current_server_ui'):
                if self.account_page.current_server_ui and self.account_page.current_server_ui.isVisible():
                    self.account_page.current_server_ui.setVisible(False)
            
            # 根据页面名称切换堆叠窗口的当前页
            if page_name == "连接管理":
                self.stacked_widget.setCurrentIndex(0)
            elif page_name == "账号管理":
                self.stacked_widget.setCurrentIndex(1)
            elif page_name == "脚本配置":
                self.stacked_widget.setCurrentIndex(2)
            elif page_name == "系统设置":
                self.stacked_widget.setCurrentIndex(3)
                
            self.append_output("系统", f"切换到 {page_name} 页面")
        except Exception as e:
            self.append_output("错误", f"切换页面时发生错误: {str(e)}")
    def create_content_panel(self):
        """创建右侧内容面板"""
        splitter = QSplitter(Qt.Vertical)
        splitter.setStyleSheet("""
            QSplitter::handle {
                background-color: rgba(0, 245, 255, 0.1);
                height: 2px;
            }
        """)
        
        # 上方主区域
        main_widget = QWidget()
        main_widget.setMinimumHeight(200)  # 设置最小高度
        main_layout = QVBoxLayout(main_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        
        # 下方输出窗口区域
        output_widget = QWidget()
        output_widget.setMinimumHeight(100)  # 设置最小高度
        output_layout = QVBoxLayout(output_widget)
        output_layout.setContentsMargins(0, 0, 0, 0)
        
        # 添加主区域
        self.create_card_area(main_layout)
        
        # 添加输出窗口
        self.create_output_window(output_layout)
        
        splitter.addWidget(main_widget)
        splitter.addWidget(output_widget)
        splitter.setSizes([600, 200])  # 设置更合理的初始尺寸
        splitter.setCollapsible(0, False)  # 禁止主区域折叠
        splitter.setCollapsible(1, False)  # 禁止操作日志区域折叠
        
        self.content_layout.addWidget(splitter)
    
    def create_card_area(self, layout):
        """创建卡片区域"""
        content_widget = QWidget()
        content_widget.setStyleSheet("""
            background-color: rgba(20, 25, 45, 0.5);
            border-radius: 8px;
            border: 1px solid rgba(0, 245, 255, 0.2);
        """)
        
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(0)
        
        # 创建堆叠窗口
        self.stacked_widget = QStackedWidget()
        self.stacked_widget.setStyleSheet("background-color: transparent;")
        
        # 创建连接管理页面(设备表格)
        self.create_phone_table_page()
        
        # 创建账号管理页面
        from UI.账号管理 import AccountManagementPage
        self.account_page = AccountManagementPage()
        self.stacked_widget.addWidget(self.account_page)
        
        # 创建脚本配置页面
        from UI.脚本配置 import ScriptConfigPage
        self.script_page = ScriptConfigPage(self)
        self.stacked_widget.addWidget(self.script_page)
        
        # 创建系统设置页面
        self.create_settings_page()
        
        content_layout.addWidget(self.stacked_widget)
        layout.addWidget(content_widget)
    
    # 表格样式常量
    PHONE_TABLE_STYLE = """
        QTableWidget {
            background-color: rgba(30, 35, 60, 0.5);
            border: 1px solid rgba(0, 245, 255, 0.2);
            color: #C0C0C0;
            font-family: "Segoe UI";
            font-size: 9pt;
            gridline-color: rgba(0, 245, 255, 0.1);
        }
        QTableWidget QTableCornerButton::section,
        QTableWidget::viewport {
            background-color: rgba(30, 35, 60, 0.5);
            border: none;
        }
        QHeaderView::section {
            background-color: rgba(106, 17, 203, 0.3);
            color: #00F5FF;
            padding-left: 8px;
            padding-right: 8px;
            border: none;
            font-family: "Segoe UI";
            font-size: 10pt;
            font-weight: normal;
        }
        QTableWidget::item {
            padding: 5px;
            text-align: center;
            vertical-align: middle;
        }
        QTableWidget::item:selected {
            background-color: rgba(37, 117, 252, 0.5);
            color: #C0C0C0;
        }
    """

    def create_phone_table_page(self):
        """创建连接管理页面(设备表格)"""
        from UI.连接管理 import ConnectionManagementPage
        self.connection_page = ConnectionManagementPage(self)
        self.stacked_widget.addWidget(self.connection_page)
    
    
    def create_settings_page(self):
        """创建系统设置页面"""
        from UI.系统设置 import SystemSettingsPage
        self.settings_page = SystemSettingsPage(self)
        self.stacked_widget.addWidget(self.settings_page)

    def create_output_window(self, layout):
        """创建输出窗口"""
        output_frame = QFrame()
        output_frame.setStyleSheet("""
            background-color: rgba(30, 35, 60, 0.5);
            border-radius: 8px;
            border: 1px solid rgba(0, 245, 255, 0.2);
        """)
        
        output_layout = QVBoxLayout(output_frame)
        output_layout.setContentsMargins(0, 0, 0, 0)
        output_layout.setSpacing(0)
        
        # 输出文本框
        self.output_text = QTextEdit()
        self.output_text.setReadOnly(True)
        self.output_text.setStyleSheet("""
            QTextEdit {
                background-color: rgba(20, 25, 45, 0.7);
                color: #E0E0E0;
                border: none;
                font-family: Consolas;
                font-size: 9pt;
                line-height: 1.4;
                padding: 10px;
            }
        """)
        
        # 创建浮空清空日志按钮
        self.clear_output_btn = QPushButton("清空日志", self.output_text)
        self.clear_output_btn.setFixedWidth(80)
        self.clear_output_btn.setStyleSheet("""
            QPushButton {
                color: #FF5555;
                background-color: rgba(106, 17, 203, 0.3);
                border: 1px solid #FF5555;
                border-radius: 4px;
                padding: 4px 8px;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: rgba(255, 85, 85, 0.5);
            }
        """)
        self.clear_output_btn.clicked.connect(self.output_text.clear)
        
        # 重写resizeEvent来更新按钮位置
        def resize_event(event):
            self.clear_output_btn.move(self.output_text.width() - 110, self.output_text.height() - 30)
            QTextEdit.resizeEvent(self.output_text, event)
        self.output_text.resizeEvent = resize_event
        
        # 初始定位按钮 (向左移动20像素避免遮挡滚动条)
        self.clear_output_btn.move(self.output_text.width() - 110, self.output_text.height() - 30)
        self.clear_output_btn.raise_()  # 确保按钮在最上层
        
        output_layout.addWidget(self.output_text)
        layout.addWidget(output_frame)
    
    def start_service(self):
        """启动服务"""
        if not self.start_btn.isEnabled():
            return
            
        try:
            ip, port = self.settings_page.get_server_info()
            
            # 禁用按钮防止重复点击
            self.start_btn.setEnabled(False)
            self.stop_btn.setEnabled(False)
            
            # 设置加载状态样式
            self.start_btn.setStyleSheet("""
            QPushButton {
                color: #606060;
                background-color: rgba(37, 117, 252, 0.1);
                border: 1px solid #606060;
                border-radius: 4px;
                padding: 6px 15px;
                font-size: 12px;
                min-width: 100px;
            }
            """)
            self.status_label.setText("启动中...")
            self.status_label.setStyleSheet("""
                QLabel {
                    color: #FFA500;
                    font-size: 14px;
                    font-weight: bold;
                    min-width: 80px;
                    text-align: center;
                }
            """)
            
            self.append_output("系统", f"正在启动服务 {ip}:{port}...")
            
            # 更新线程配置并发射启动信号
            self.thread.set_server_info(ip, port)
            self.startService.emit(ip, port)
            
        except Exception as e:
            self.append_output("错误", f"启动服务失败: {str(e)}")
            self._reset_buttons()
    

    def _reset_buttons(self):
        """重置按钮状态"""
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        
        # 启动按钮闪烁动画效果
        self.start_btn.setStyleSheet("""
            QPushButton {
                color: #00FF00;
                background-color: rgba(106, 17, 203, 0.3);
                border: 1px solid #00FF00;
                border-radius: 4px;
                padding: 6px 15px;
                font-size: 12px;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: rgba(0, 255, 0, 0.5);
            }
        """)
        
        # 停止按钮禁用状态
        self.stop_btn.setStyleSheet("""
            QPushButton {
                color: #606060;
                background-color: rgba(106, 17, 203, 0.1);
                border: 1px solid #606060;
                border-radius: 4px;
                padding: 6px 15px;
                font-size: 12px;
                min-width: 100px;
            }
        """)
        
        # 状态标签脉冲动画效果
        self.status_label.setText("服务未启动")
        self.status_label.setStyleSheet("""
            QLabel {
                color: #FF5555;
                font-size: 14px;
                font-weight: bold;
                min-width: 80px;
                text-align: center;
            }
        """)

    def stop_service(self):
        """停止服务"""
        if not self.stop_btn.isEnabled():
            return
            
        try:
            # 创建赛博朋克风格确认对话框
            msg_box = QMessageBox()
            msg_box.setWindowTitle("确认停止服务")
            msg_box.setText("确定要停止服务吗？")
            msg_box.setStandardButtons(QMessageBox.Ok | QMessageBox.Cancel)
            msg_box.setDefaultButton(QMessageBox.Cancel)
            
            # 设置对话框显示在主窗口中央
            def adjust_position():
                center = self.geometry().center()
                msg_box.move(center - msg_box.rect().center())
            
            # 设置赛博朋克样式 - 增强版
            msg_box.setStyleSheet("""
                QMessageBox {
                    background-color: rgba(20, 25, 45, 0.98);
                    border: 2px solid #00F5FF;
                    border-top: 3px solid qlineargradient(
                        x1:0, y1:0, x2:0, y2:1,
                        stop:0 #00F5FF, stop:1 transparent
                    );
                }
                QLabel {
                    color: #00F5FF;
                    font-size: 16px;
                    font-weight: bold;
                    margin: 15px 5px;
                    padding: 5px;
                }
                QPushButton {
                    color: #FFFFFF;
                    background-color: rgba(106, 17, 203, 0.5);
                    border: 1px solid #00F5FF;
                    border-radius: 4px;
                    padding: 8px 20px;
                    min-width: 100px;
                    font-size: 14px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: rgba(0, 245, 255, 0.7);
                    color: #000000;
                    border: 1px solid #FFFFFF;
                }
                QPushButton:pressed {
                    background-color: rgba(0, 245, 255, 0.9);
                }
            """)
            
            # 显示后立即调整位置
            msg_box.show()
            adjust_position()
            msg_box.setStyleSheet("""
                QMessageBox {
                    background-color: rgba(20, 25, 45, 0.95);
                    border: 2px solid #00F5FF;
                    border-top: 3px solid qlineargradient(
                        x1:0, y1:0, x2:0, y2:1,
                        stop:0 #00F5FF, stop:1 transparent
                    );
                }
                QLabel {
                    color: #00F5FF;
                    font-size: 14px;
                    margin-top: 5px;
                }
                QPushButton {
                    color: #00F5FF;
                    background-color: rgba(106, 17, 203, 0.3);
                    border: 1px solid #00F5FF;
                    border-radius: 4px;
                    padding: 5px 15px;
                    min-width: 80px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: rgba(0, 245, 255, 0.5);
                    color: white;
                    border: 1px solid white;
                }
            """)
            
            if msg_box.exec_() == QMessageBox.Ok:
                # 设置停止中状态
                self.stop_btn.setEnabled(False)
                self.stop_btn.setStyleSheet("""
                    QPushButton {
                        color: #808080;
                        background-color: rgba(106, 17, 203, 0.1);
                        border: 1px solid #808080;
                        border-radius: 4px;
                        padding: 6px 15px;
                        font-size: 12px;
                        min-width: 100px;
                    }
                """)
                self.status_label.setText("停止中...")
                self.status_label.setStyleSheet("""
                    QLabel {
                        color: #FFA500;
                        font-size: 14px;
                        font-weight: bold;
                        min-width: 80px;
                        text-align: center;
                    }
                """)
                
                # 发射停止信号
                self.stopService.emit()
                
        except Exception as e:
            self.append_output("错误", f"停止服务时发生错误: {str(e)}")
            self._reset_buttons()
    
    def append_output(self, typ, msg):
        """向输出窗口添加消息"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 根据类型设置颜色
        color = "#C0C0C0"  # 默认灰色
        if typ == "错误":
            color = "#FF5555"
        elif typ == "警告":
            color = "#FFA500"
        elif typ == "系统":
            color = "#00F5FF"
        elif typ == "信息":
            color = "#00FF00"
        
        # 使用HTML格式添加彩色文本
        self.output_text.append(f'<font color="{color}">[{timestamp}] [{typ}] {msg}</font>')
        # 自动滚动到最新内容
        scroll_bar = self.output_text.verticalScrollBar()
        scroll_bar.setValue(scroll_bar.maximum())
    
    def update_ui(self, message):
        """
        更新UI的槽函数
        :param message: 接收到的消息文本
        """
        self.append_output("信息", message)
        
        # 根据消息内容更新UI状态
        if message == "update_table":
            # 处理表格更新信号
            if hasattr(self, 'connection_page'):
                # 获取当前连接设备并更新表格
                if hasattr(self.connection_page, 'phone_table'):
                    table = self.connection_page.phone_table
                    for row in range(table.rowCount()):
                        device_id = table.item(row, 0).text()  # 第0列是设备ID
                        device_info = {
                            "device_id": device_id,
                            "status": "已连接"  # 保持状态不变
                        }
                        self.connection_page.update_device_table(device_info)
        elif "服务启动" in message:
            # 启动成功后启用停止按钮
            self.stop_btn.setEnabled(True)
            self.stop_btn.setStyleSheet("""
                QPushButton {
                    color: #FF5555;
                    background-color: rgba(106, 17, 203, 0.3);
                    border: 1px solid #FF5555;
                    border-radius: 4px;
                    padding: 6px 15px;
                    font-size: 12px;
                    min-width: 100px;
                }
                QPushButton:hover {
                    background-color: rgba(255, 85, 85, 0.5);
                }
            """)
            self.status_label.setText(f"服务已启动 {self.thread.ip}:{self.thread.port}")
            self.status_label.setStyleSheet("""
                QLabel {
                    color: #00FF00;
                    font-size: 14px;
                    font-weight: bold;
                    min-width: 80px;
                    text-align: center;
                }
            """)
        elif "服务关闭" in message:
            # 停止服务后重置按钮状态
            self._reset_buttons()
    
    def update_device_table(self, device_info):
        """更新设备表格(带防抖机制)"""
        try:
            # 为测试数据生成唯一ID
            if device_info["device_id"] == "测试ID":
                device_info["device_id"] = f"测试ID_{time.time()}"
            
            device_id = device_info["device_id"]
            
            # 通过connection_page更新表格
            if hasattr(self, 'connection_page'):
                self.connection_page.update_device_table(device_info)
            else:
                self.append_output("错误", "连接管理页面未初始化")
        except Exception as e:
            self.append_output("错误", f"更新设备表格失败: {str(e)}")

    
    def handle_update_action(self, device_id: str, action: str):
        """处理设备动作更新信号"""
        try:
            if not hasattr(self, 'connection_page'):
                return
                
            table = self.connection_page.phone_table
            if table.columnCount() <= 9:  # 检查列数是否足够
                return
                
            for row in range(table.rowCount()):
                item = table.item(row, 0)  # 第0列是device_id
                if item and item.text() == device_id:
                    # 更新当前动作列(第9列)
                    action_item = QTableWidgetItem(action)
                    table.setItem(row, 9, action_item)
                    # 强制刷新表格
                    table.viewport().update()
                    日志.log_message(f"{device_id} 当前动作: {action}", "信息")
                    return
            
            日志.log_message(f"未找到设备ID {device_id} 对应的设备", "警告")
            
        except Exception as e:
            日志.log_message(f"handle_update_action错误: {str(e)}", "error")

    def handle_update_mode(self, device_id: str, mode: str):
        """处理设备操作模式更新信号"""
        try:
            if not hasattr(self, 'connection_page'):
                return
                
            table = self.connection_page.phone_table
            if table.columnCount() <= 10:  # 检查列数是否足够
                return
                
            for row in range(table.rowCount()):
                item = table.item(row, 0)  # 第0列是device_id
                if item and item.text() == device_id:
                    # 更新操作模式列(第10列)
                    mode_item = QTableWidgetItem(mode)
                    mode_item.setTextAlignment(Qt.AlignCenter)
                    table.setItem(row, 10, mode_item)
                    # 强制刷新表格
                    table.viewport().update()
                    日志.log_message(f"{device_id} 操作模式: {mode}", "信息")
                    return
            
            日志.log_message(f"未找到设备ID {device_id} 对应的设备", "警告")
            
        except Exception as e:
            日志.log_message(f"handle_update_mode错误: {str(e)}", "错误")

    def center_dialog(self, dialog):
        """将对话框居中显示在主窗口"""
        dialog_rect = dialog.rect()
        main_rect = self.geometry()
        
        # 计算居中位置
        x = main_rect.x() + (main_rect.width() - dialog_rect.width()) // 2
        y = main_rect.y() + (main_rect.height() - dialog_rect.height()) // 2
        
        # 确保对话框不会超出屏幕
        screen_rect = QApplication.desktop().availableGeometry()
        x = max(0, min(x, screen_rect.width() - dialog_rect.width()))
        y = max(0, min(y, screen_rect.height() - dialog_rect.height()))
        
        dialog.move(x, y)

    def update_device_row(self, row, device_info):
        """此方法已废弃，通过connection_page处理行更新"""
        pass

# PyQt5 后台线程类,继承自QThread，负责在后台运行Android服务        
class AndroidControlThread(QThread):
    """Android设备控制线程"""
    progress = pyqtSignal(str)  # 简化为直接发送消息文本

    def __init__(self):
        super().__init__()
        self._load_config()
        self.ip = "127.0.0.1"
        self.port = 8888
        self.stop_flag = False
        
        # 创建定时器用于表格更新防抖
        self.table_update_timer = QTimer()
        self.table_update_timer.setSingleShot(True)
        self.table_update_timer.timeout.connect(self._perform_table_update)

    def _load_config(self):
        """从注册表加载配置"""
        try:
            key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, r"Software\Aibote\ServerConfig")
            self.ip = winreg.QueryValueEx(key, "ServerIP")[0]
            self.port = int(winreg.QueryValueEx(key, "ServerPort")[0])
            winreg.CloseKey(key)
        except:
            self.ip, self.port = "127.0.0.1", 8888  # 默认值
    def run(self):
        """线程主运行方法"""
        try:
            self.progress.emit(f'服务启动成功 IP:{self.ip} 端口:{self.port}')
            # 启动定时器
            self.table_update_timer.start(100)
            # 在新线程中执行服务启动
            from threading import Thread
            Thread(target=CustomAndroidScript.execute, args=(self.ip, self.port), daemon=True).start()
        except Exception as e:
            self.progress.emit(f'服务启动失败: {str(e)}')

    def stop_service(self):
        """停止服务"""
        try:
            CustomAndroidScript.StopSrver()
            self.table_update_timer.stop()
            self.progress.emit('服务关闭成功')
        except Exception as e:
            self.progress.emit(f'服务关闭失败: {str(e)}')

    def _perform_table_update(self):
        """定时器触发时执行表格更新"""
        try:
            # 通过信号通知主线程更新表格
            self.progress.emit('update_table')
        except Exception as e:
            self.progress.emit(f'表格更新失败: {str(e)}')

    def set_server_info(self, ip, port):
        """设置服务器信息"""
        self.ip, self.port = ip, port

# 2. 自定义一个脚本类,Android设备控制脚本类,继承 AndroidBotMain,实现具体的设备操作逻辑
class CustomAndroidScript(AndroidBotMain):
    """Android设备控制脚本"""
    Log_Level = "INFO"    # 日志级别 INFO不打印
    Log_Storage = False     # 是否存储日志
    resolution_a = (2400, 1080)  # 抓取过坐标的设备标准分辨率
    # resolution_a = (1920, 1080)  # 抓取过坐标的设备标准分辨率
    _instances = {}  # 类变量保存所有实例 {device_id: instance}
    
    # 4. Hid底层是windows来操作的所以需要windows驱动
    WinDriving = WinBotMain._build("127.0.0.1", 7837, True)
    WinDriving.init_hid()
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.android_id = None #手机设备ID
        self.device_mode = None #手机操作类型HID和无障碍
        self.resolution_b = None #需要转换的设备分辨率
        self.登录区服 = None  # 登录区服
        self.业务标记 = None  # 初始化业务标记属性            
    def script_main(self):
        """主业务逻辑，由框架自动调用"""
        # 获取手机连接信息
        Android.get_Connection_information(self)
        self.登录区服 = 账号管理.查询并更新连接管理区服(self.android_id)
        #添加实例对象到类字典对象
        self.__class__._instances[self.android_id] = self 
        # print(f"实例对象: {self.__class__._instances.get(self.android_id)}")
        self.device_mode = Android.check_device_mode(self) #获取手机操作类型
        if self.device_mode:
            print(self.resolution_a)
            # print(self.resolution_b)          
            # for i in range(99):
            #     # get =Android.找色_单击(self,"#000000",[[16, 10, "#000000"], [16, 16, "#000000"], [24, 20, "#000000"]],(2094, 21, 2177, 65),1)
            #     get = 游戏内检测.进入游戏检测(self)
            #     # get = self._parsing_ocr_data((1873, 71, 1950, 127),(0,0,0),1)
            #     print(get)
            #     time.sleep(3)
            # 手机连接进来检测需要做什么任务
            # for i in range(100):
            #     get = self.get_text((0,0,0,0), (0,0,0), 1)
            #     print(get)
            #     time.sleep(1)
            # # #=========================截图==================
            # # # 截图坐标
            # result = self.take_screenshot((0, 0, 0, 0), (0,0,0), 1)
            # print(result) 
            # # 将字节流保存为图片使用示例 [Demo]
            # from PIL import Image
            # import io
            # image = Image.open(io.BytesIO(result))
            # image.save(r'C:\Users\<USER>\Desktop\1.png')
            # print("完成")
            # time.sleep(5)
            # result = self.get_image_size(r'C:\Users\<USER>\Desktop\1.png')  # 读取根目录中1.png图片大小
            # print(result)
            # time.sleep(10)
            # # #=========================截图==================
            # 任务检测(self)
            # self.业务标记 = 12
            # while True:
            for i in range(********):
                try:
                    # 任务分发(self)
                    result1 = self.app_is_running("微啊信")
                    print(result1)
                    time.sleep(0.3)
                    连接管理.update_device_action_by_id(self.android_id,str(i))
                    # 账号管理.update_account_remark_by_device(self.android_id,str(i))
                except OSError as e:
                    print(f"连接断开: {e}")
                    ip = str(e).split(":")[0]
                    连接管理.delete_row_by_ip(ip)#删除连接数据
                    日志.log_message(f"ID: {self.android_id}客户端手动断开连接","警告")
                    账号管理.update_account_login_status_by_device(self.android_id,"手动断开") #将账号管理设置
                    break
                except Exception as e:
                    ip = str(e).split(":")[0]
                    连接管理.delete_row_by_ip(ip) #删除连接数据
                    账号管理.update_account_login_status_by_device(self.android_id,"异常断开")
                    日志.log_message(f"ID: {self.android_id} 客户端异常断开连接", "警告")
                    break
        else:
            日志.log_message(f"ID: {self.android_id}设置操作类型无法获取关闭连接","警告")
            if self.close_driver():
                日志.log_message(f"ID: {self.android_id}无法获取操作模式关闭手机连接成功","信息")
            else:
                日志.log_message(f"ID: {self.android_id}无法获取操作模式关闭手机连接失败","警告")                         

# 程序入口
# 1. 创建一个主函数，负责启动应用程序
if __name__ == "__main__":
    
    app = QApplication(sys.argv)
    app.setStyle("Fusion")
    
    # 使用更兼容的方式注册元类型
    try:
        # 通过信号连接自动注册类型
        app.aboutToQuit.connect(lambda: None)  # 触发元类型系统初始化
    except Exception as e:
        print(f"初始化元类型系统时出错: {str(e)}")
    
    # 高DPI设置(已在文件开头设置)
    QApplication.setHighDpiScaleFactorRoundingPolicy(
        Qt.HighDpiScaleFactorRoundingPolicy.PassThrough
    )
    
    try:
        window = CyberpunkMainWindow()
        window.show()
        sys.exit(app.exec_())
    except Exception as e:
        print(f"Error: {str(e)}")
        print(traceback.format_exc())
