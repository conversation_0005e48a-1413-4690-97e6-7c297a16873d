from PyQt5.QtCore import Qt
from PyQt5.QtWidgets import (QWidget, QHBoxLayout, QVBoxLayout, QLabel, 
                           QFrame, QListWidget, QTableWidget, 
                           QTableWidgetItem, QSplitter, QPushButton,
                           QListWidgetItem, QHeaderView)
import sqlite3
from pprint import pprint

class DatabaseTab(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.main_window = parent
        self.db_path = "DB/account.db"
        self.init_ui()
        # 移除初始化时的表加载

    def init_ui(self):
        """初始化数据库管理UI"""
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(5)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        
        # 左侧表列表
        self.table_list = QListWidget()
        self.table_list.setStyleSheet("""
            QListWidget {
                background-color: rgba(20, 25, 45, 0.5);
                color: #00F5FF;
                border: 1px solid rgba(0, 245, 255, 0.2);
                border-radius: 8px;
            }
            QListWidget::item:hover {
                background-color: rgba(0, 245, 255, 0.2);
            }
            QListWidget::item:selected {
                background-color: rgba(106, 17, 203, 0.3);
            }
        """)
        self.table_list.itemClicked.connect(self.show_table_structure)
        
        # 右侧结构表格
        self.structure_table = QTableWidget()
        self.structure_table.setStyleSheet("""
            QTableWidget {
                background-color: rgba(20, 25, 45, 0.5);
                color: #00F5FF;
                border: 1px solid rgba(0, 245, 255, 0.2);
                border-radius: 8px;
                selection-background-color: transparent;
                selection-color: #00F5FF;
            }
            QHeaderView::section {
                background-color: rgba(106, 17, 203, 0.3);
                color: #00F5FF;
                padding: 5px;
                border: none;
            }
        """)
        # 完全禁用选择和编辑行为
        self.structure_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.structure_table.setSelectionMode(QTableWidget.NoSelection)
        self.structure_table.setFocusPolicy(Qt.NoFocus)
        self.structure_table.setSelectionBehavior(QTableWidget.SelectItems)
        self.structure_table.setColumnCount(5)
        self.structure_table.setHorizontalHeaderLabels(
            ["字段名", "类型", "允许NULL", "默认值", "主键"])
        # 设置表格内容居中
        self.structure_table.horizontalHeader().setDefaultAlignment(Qt.AlignCenter)
        # 设置表格自动扩展
        self.structure_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        
        # 添加组件到分割器并设置初始比例
        splitter.addWidget(self.table_list)
        splitter.addWidget(self.structure_table)
        splitter.setSizes([70, 500])  # 左侧宽度150px，右侧500px
        
        
        # 底部按钮
        btn_refresh = QPushButton("刷新")
        btn_refresh.setStyleSheet("""
            QPushButton {
                background-color: rgba(106, 17, 203, 0.3);
                color: #00F5FF;
                border: 1px solid rgba(0, 245, 255, 0.3);
                border-radius: 4px;
                padding: 5px 10px;
            }
            QPushButton:hover {
                background-color: rgba(0, 245, 255, 0.2);
            }
        """)
        btn_refresh.clicked.connect(self.load_tables)
        
        # 主布局
        layout = QVBoxLayout()
        layout.addWidget(splitter)
        layout.addWidget(btn_refresh, 0, Qt.AlignRight)
        main_layout.addLayout(layout)

    def load_tables(self):
        """加载数据库表列表"""
        self.table_list.clear()
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall() if not row[0].startswith('sqlite_')]
            conn.close()
            
            for table in tables:
                item = QListWidgetItem(table)
                item.setTextAlignment(Qt.AlignCenter)
                self.table_list.addItem(item)
            
            if tables:
                self.table_list.setCurrentRow(0)
                self.show_table_structure()
        except Exception as e:
            print(f"[错误] 加载数据库失败: {str(e)}")

    def show_table_structure(self):
        """显示选中表的结构"""
        table_name = self.table_list.currentItem().text()
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            conn.close()
            
            self.structure_table.setRowCount(len(columns))
            for row, col in enumerate(columns):
                for i in range(5):
                    item = QTableWidgetItem(str(col[i+1] if i < len(col)-1 else ""))
                    item.setTextAlignment(Qt.AlignCenter)
                    self.structure_table.setItem(row, i, item)
            
            print(f"[系统] 已加载表结构: {table_name}")
        except Exception as e:
            print(f"[错误] 加载表结构失败: {str(e)}")
