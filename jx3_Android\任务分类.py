from Aibote import Android
import time
from .随机触发函数 import 基础动作
from window_func import 连接管理, 日志, 账号管理
from .登录模块 import 登录区服,创建角色,选择区服
from .剑三通用函数 import 区服函数,游戏内通用
from .业务检测 import 登录界面,游戏内检测

def 任务分发(obj):
    """任务分类器函数（静态方法优化版）"""
    while True:
        try:
            match obj.业务标记:
                case 1:
                    print("处理登录")
                    任务分类_登录界面.处理登录(obj)  # 改为静态方法调用
                case 11:
                    print("选择区服")
                    任务分类_登录界面.选择区服(obj)
                    time.sleep(1)
                case 12:
                    print("创建角色点击")
                    任务分类_登录界面.创建角色点击(obj)    
                case 13:
                    print("选择角色")
                    任务分类_登录界面.选择角色(obj)   
                case 2:
                    print("稻香村任务")
                    任务分类_游戏内.选择角色(obj)      
                    time.sleep(1)        
                case _:
                    print(f"未知业务标记: {obj.业务标记}")
            
            # 随机间隔触发保持不变
            # random_action = 基础动作(obj)
            # random_action.时间间隔触发(最小间隔=500, 最大间隔=615)
            
        except Exception as e:
            print(f"任务分发失败: {e}")
            result = obj.close_driver() 
            print(result)

class 任务分类_登录界面:
    """任务处理器（静态方法）"""   
    @staticmethod
    def 处理登录(obj):
        """处理登录"""
        if not hasattr(obj, "_区服完成状态"):
            obj._区服完成状态 = -1  # 初始化状态
        登录区服.关闭公告弹窗(obj)    
        登录区服.关闭性能提示(obj)
        登录区服.点击取消(obj)
        #获取区服去数据库查询区服任务是否完成.完成就给区服完成状态赋值True
        if obj._区服完成状态 == -1:
            get = 登录区服.处理区服选择(obj)
            if get == 11:
                #已完成,选其他区服
                obj._区服完成状态 = 11
            elif get == 12: 
                #未完成,点击登录游戏
                 obj._区服完成状态 = 12
        print(f"区服完成状态: {obj._区服完成状态}")
        if obj._区服完成状态 == 11:
            #任务已完成选择其他区服
            选择区服.进入选择区服点击(obj)
        if obj._区服完成状态 == 12:
            #任务未完成点击登录游戏
            登录区服.点击登录按钮(obj)
        if obj._区服完成状态 > -1:
            if 登录界面.选择服务器检测(obj):
                obj.业务标记 =11 #选择区服12
            if 登录界面.创建角色检测(obj):
                obj.业务标记 =12 #创建角色11
            if 登录界面.选择角色检测(obj):
                obj.业务标记 =13 #创建角色11


    @staticmethod
    def 创建角色点击(obj):
        """创建角色逻辑"""
        创建角色.点击随机名字(obj)
        创建角色.点击纯阳门派(obj)
        创建角色.点击下一步一(obj)
        创建角色.点击下一步二(obj)
        创建角色.点击下一步三(obj)
        创建角色.进入游戏(obj)
        if 创建角色.点卡不足检测(obj):
            账号管理.update_account_remark_by_device(obj.android_id, "点卡不足")
            日志.log_message(f"设备 {obj.android_id} 点卡不足", "警告")
        游戏内通用.跳过剧情点击(obj)
        游戏内通用.弹窗_确认_点击(obj)
        if 游戏内检测.进入游戏检测(obj):
            obj.业务标记 = 2 #进入游戏15
        
        
                
    @staticmethod
    def 选择区服(obj):
        """选择区服"""
        if not hasattr(obj, "_是否在电信区"):
            obj.是否在电信区 = False
        if not hasattr(obj, "_选择区服"):
            obj._选择区服 = None
        if not hasattr(obj, "选中的区服"):
            obj.选中的区服 = False         
        # 获取未完成区服
        if obj._选择区服 is None:
            obj._选择区服 = 区服函数.随机获取未完成区服名称(obj.android_id)
            if obj._选择区服 is None:
                日志.log_message(f"设备 {obj.android_id} 无可用区服", "警告")
                return False
        print(obj._选择区服)
        if obj._选择区服:
            #获取成功选择区服后才执行
            选择区服.进入选择区服点击(obj)
            if 选择区服.点击指定区服(obj,obj._选择区服):
                obj.选中的区服 = True
        if not obj.选中的区服:
            #没有选择区服的时候才点击电信区      
            选择区服.点击电信区(obj)
        if obj.选中的区服:
            # 选择区服确认才点击确认选择
            if 选择区服.确认选择点击(obj):    
                #更新账号管理登录区服和UI
                obj._区服完成状态 = 12
                obj.业务标记 =1
                账号管理.update_account_server_by_device(obj.android_id, obj._选择区服)
                #更新连接管理区服
                连接管理.update_device_server_by_id(obj.android_id, obj._选择区服)             

    @staticmethod
    def 选择角色(obj):
        """选择角色"""
        print("选择角色")
class 任务分类_游戏内:
    @staticmethod
    def 稻香村任务(obj):
        """稻香村任务"""
        print("稻香村任务")
