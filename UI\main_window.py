"""
主窗口UI组件模块
包含主窗口的UI布局和组件组装
"""
from PyQt5.QtCore import Qt, QPoint, pyqtSignal
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                            QFrame, QSplitter, QStackedWidget, QApplication)
from .styles import UIStyles
from .title_bar import TitleBar
from .navigation_panel import NavigationPanel
from .output_window import OutputWindow

class MainWindowUI(QMainWindow):
    """主窗口UI组件"""
    
    # 定义信号
    start_service_requested = pyqtSignal(str, int)  # ip, port
    stop_service_requested = pyqtSignal()
    page_switched = pyqtSignal(str)  # 页面名称
    
    def __init__(self):
        super().__init__()
        self.init_window_properties()
        self.init_ui_components()
        self.init_mouse_drag()
        
    def init_window_properties(self):
        """初始化窗口属性"""
        self.setWindowFlags(Qt.FramelessWindowHint)
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.setFixedSize(1000, 600)
        
    def init_ui_components(self):
        """初始化所有UI组件"""
        # 创建主容器和主布局
        self.main_container = QWidget()
        self.main_container.setObjectName("MainContainer")
        self.setCentralWidget(self.main_container)
        
        self.main_layout = QVBoxLayout(self.main_container)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(0)
        
        # 创建标题栏
        self.create_title_bar()
        
        # 创建主内容区域
        self.create_main_content()
        
        # 设置基础样式
        self.setup_styles()
        
    def init_mouse_drag(self):
        """初始化鼠标拖动相关变量"""
        self._is_dragging = False
        self._drag_start_position = None
        
    def create_title_bar(self):
        """创建标题栏"""
        self.title_bar = TitleBar(self)
        
        # 连接标题栏信号
        self.title_bar.minimize_requested.connect(self.showMinimized)
        self.title_bar.close_requested.connect(self.close)
        self.title_bar.start_service_requested.connect(self._handle_start_service)
        self.title_bar.stop_service_requested.connect(self.stop_service_requested.emit)
        
        self.main_layout.addWidget(self.title_bar)
        
    def create_main_content(self):
        """创建主内容区域"""
        content_frame = QFrame()
        content_frame.setFrameShape(QFrame.NoFrame)
        content_frame.setStyleSheet("background-color: transparent;")
        
        self.content_layout = QHBoxLayout(content_frame)
        self.content_layout.setContentsMargins(0, 0, 0, 0)
        self.content_layout.setSpacing(5)
        
        # 左侧导航区
        self.create_navigation_panel()
        
        # 右侧内容区
        self.create_content_panel()
        
        self.main_layout.addWidget(content_frame)
        
    def create_navigation_panel(self):
        """创建左侧导航面板"""
        self.navigation_panel = NavigationPanel(self)
        
        # 连接导航面板信号
        self.navigation_panel.page_changed.connect(self._handle_page_change)
        
        self.content_layout.addWidget(self.navigation_panel)
        
    def create_content_panel(self):
        """创建右侧内容面板"""
        splitter = QSplitter(Qt.Vertical)
        splitter.setStyleSheet(UIStyles.SPLITTER_STYLE)
        
        # 上方主区域
        main_widget = QWidget()
        main_widget.setMinimumHeight(200)  # 设置最小高度
        main_layout = QVBoxLayout(main_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        
        # 下方输出窗口区域
        output_widget = QWidget()
        output_widget.setMinimumHeight(100)  # 设置最小高度
        output_layout = QVBoxLayout(output_widget)
        output_layout.setContentsMargins(0, 0, 0, 0)
        
        # 添加主区域
        self.create_card_area(main_layout)
        
        # 添加输出窗口
        self.create_output_window(output_layout)
        
        splitter.addWidget(main_widget)
        splitter.addWidget(output_widget)
        splitter.setSizes([600, 200])  # 设置更合理的初始尺寸
        splitter.setCollapsible(0, False)  # 禁止主区域折叠
        splitter.setCollapsible(1, False)  # 禁止操作日志区域折叠
        
        self.content_layout.addWidget(splitter)
        
    def create_card_area(self, layout):
        """创建卡片区域"""
        content_widget = QWidget()
        content_widget.setStyleSheet(UIStyles.CONTENT_PANEL_STYLE)
        
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(0)
        
        # 创建堆叠窗口
        self.stacked_widget = QStackedWidget()
        self.stacked_widget.setStyleSheet("background-color: transparent;")
        
        content_layout.addWidget(self.stacked_widget)
        layout.addWidget(content_widget)
        
    def create_output_window(self, layout):
        """创建输出窗口"""
        self.output_window = OutputWindow(self)
        layout.addWidget(self.output_window)
        
    def setup_styles(self):
        """设置基础样式"""
        self.main_container.setStyleSheet(UIStyles.MAIN_CONTAINER_STYLE)
        QApplication.setFont(UIStyles.BODY_FONT)
        
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.LeftButton:
            self._is_dragging = True
            self._drag_start_position = event.globalPos() - self.frameGeometry().topLeft()
            event.accept()

    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        if self._is_dragging and event.buttons() == Qt.LeftButton:
            self.move(event.globalPos() - self._drag_start_position)
            event.accept()

    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if event.button() == Qt.LeftButton:
            self._is_dragging = False
            event.accept()
            
    def _handle_start_service(self):
        """处理启动服务请求"""
        # 发射信号让业务层处理，业务层会获取正确的服务器信息
        self.start_service_requested.emit("127.0.0.1", 8888)  # 默认值，业务层会覆盖
        
    def _handle_page_change(self, page_name, button):
        """处理页面切换"""
        self.page_switched.emit(page_name)
        
    def add_page(self, widget):
        """添加页面到堆叠窗口"""
        return self.stacked_widget.addWidget(widget)
        
    def set_current_page(self, index):
        """设置当前页面"""
        self.stacked_widget.setCurrentIndex(index)
        
    def get_current_page(self):
        """获取当前页面"""
        return self.stacked_widget.currentWidget()
        
    def append_output(self, typ, msg):
        """向输出窗口添加消息"""
        self.output_window.append_output(typ, msg)
        
    def update_service_status(self, message, ip=None, port=None):
        """更新服务状态"""
        self.title_bar.update_service_status(message, ip, port)
        
    def reset_service_buttons(self):
        """重置服务按钮状态"""
        self.title_bar.reset_buttons()
        
    def center_dialog(self, dialog):
        """将对话框居中显示在主窗口"""
        dialog_rect = dialog.rect()
        main_rect = self.geometry()
        
        # 计算居中位置
        x = main_rect.x() + (main_rect.width() - dialog_rect.width()) // 2
        y = main_rect.y() + (main_rect.height() - dialog_rect.height()) // 2
        
        # 确保对话框不会超出屏幕
        screen_rect = QApplication.desktop().availableGeometry()
        x = max(0, min(x, screen_rect.width() - dialog_rect.width()))
        y = max(0, min(y, screen_rect.height() - dialog_rect.height()))
        
        dialog.move(x, y)
