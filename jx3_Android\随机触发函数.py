import random
import time
from Aibote import Android

class 基础动作:
    def __init__(self, bot_instance):
        self.bot = bot_instance
        self.屏幕宽度 = 1080
        self.屏幕高度 = 2400
        self._last_trigger_time = 0
    
    def 执行随机动作(self, 概率=1.0):
        if random.random() < 概率:
            动作列表 = [
                "随机移动",
                "短暂停留", 
                "无效点击",
                "随机滑动",
                "快速连点"
            ]
            选中动作 = random.choice(动作列表)
            print(f"触发随机动作: {选中动作}")
            return True
        return False
        
    def 时间间隔触发(self, 最小间隔=5, 最大间隔=15):
        now = time.time()
        if now - self._last_trigger_time > random.randint(最小间隔, 最大间隔):
            self._last_trigger_time = now
            return self.执行随机动作(概率=1.0)
        return False

    def 模拟随机移动(self):
        print("模拟触发: 随机移动")
        
    def 模拟短暂停留(self):
        print("模拟触发: 短暂停留")
        
    def 模拟无效点击(self):
        print("模拟触发: 无效点击")
        
    def 模拟随机滑动(self):
        print("模拟触发: 随机滑动")
        
    def 模拟快速连点(self):
        print("模拟触发: 快速连点")
