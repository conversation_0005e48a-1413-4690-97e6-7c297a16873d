import time
from Aibote import Android
from window_func import 连接管理, 日志
def 任务检测(obj):
    for i in range(99):
        try:
            print("任务检测")
            # result = obj._parsing_ocr_data((1911, 933, 2130, 1050), (0,0,0), 1) 
            # print(f"get: {result}")
            # # result = obj.find_text("下一步", (1938, 951, 2115, 1050), (0,0,0), 1)  
            # print(f"find: {result}")
            if 登录界面.登录界面检测(obj):
                obj.业务标记 = 1  # 在登录界面
                break
            if 登录界面.选择服务器检测(obj):
                obj.业务标记 = 11  # 在登录界面
                break
            if 登录界面.创建角色检测(obj):
                obj.业务标记 = 12  # 在登录界面
                break
            if 登录界面.选择角色检测(obj):
                obj.业务标记 = 13  # 在登录界面
                break
        except Android.AndroidBotException as e:
            日志.log_message(f"Android操作异常: {e}", "错误")
            #关闭远程连接
            print("任务检测断开连接")
            result = obj.close_driver() 
            print(result)
            break
        except Exception as e:
            日志.log_message(f"任务检测异常: {e}", "错误")
        time.sleep(0.2)
class 登录界面:
    @staticmethod
    def 登录界面检测(obj):
        # print("登录界面检测")
        #检测性能低弹窗
        get1 = Android.find_text_bool(obj, "低", region=(915, 516, 955, 561))
        #账号登录界面的登录
        get2 = Android.find_text_bool(obj, "公告", region=(534, 471, 639, 552))
        #公告弹窗
        get3 = Android.find_text_bool(obj, "游戏日志", region=(2141, 593, 2260, 641))
        if get1 or get2 or get3:
            连接管理.update_device_map(obj.android_id,"登录界面")
            return True
    @staticmethod
    def 选择服务器检测(obj):
        get1 = Android.find_text_bool(obj, "大区", region=(174, 5, 280, 81))
        if get1:
            连接管理.update_device_map(obj.android_id,"服务器选择")
            return True
    @staticmethod
    def 创建角色检测(obj):
        # print("创建角色检测")
        #通过找下一步判断是否在创建角色界面
        get1 = Android.find_text_bool(obj, "下", region=(1943, 962, 2109, 1034),algorithm= (0,220,255))
        #输入角色名字界面
        get2 = Android.find_text_bool(obj, "名字", region=(1656, 254, 1758, 319),algorithm= (0,120,255))
        #输入角色名字界面
        get3 = Android.find_text_bool(obj, "分享照片", region=(2032, 418, 2173, 474))
        if get1 or get2 or get3:
            连接管理.update_device_map(obj.android_id,"创建角色")
            return True
    @staticmethod            
    def 选择角色检测(obj):
        """选择角色检测"""
        if Android.find_text_bool(obj, "角色选择", region=(180, 11, 375, 73)):
            连接管理.update_device_map(obj.android_id, "选择角色")
            return True
class 游戏内检测:
    @staticmethod
    def 进入游戏检测(obj):
        get = Android.find_text_bool(obj, "角色", region=(1873, 71, 1950, 127))#找的是游戏内的角色字体,在小地图左上方
        get1 = Android.找字_匹配_单击(obj, "设置", region=(1192, 688, 1295, 748)) #主界面设置弹窗_确认设置
        if get or get1:
            连接管理.update_device_map(obj.android_id,"进入游戏检测")
            return True
