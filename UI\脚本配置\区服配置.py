from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, 
                            QTableWidgetItem, QHeaderView, QPushButton, 
                            QMessageBox, QInputDialog, QMainWindow)
from PyQt5.QtCore import Qt
import sqlite3
import window_func

class ServerTagManager:
    def __init__(self, db_path):
        self.db_path = db_path
        self.init_table()

    def init_table(self):
        """初始化时创建表结构"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS 区服标签 (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    标签名称 TEXT NOT NULL UNIQUE,
                    创建时间 TEXT DEFAULT CURRENT_TIMESTAMP
                )
            """)
            conn.commit()
        except Exception as e:
            print(f"初始化表失败: {str(e)}")
        finally:
            conn.close()

    def add_tag(self, name):
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("INSERT INTO 区服标签 (标签名称) VALUES (?)", (name,))
            conn.commit()
            return True
        except sqlite3.IntegrityError:
            raise Exception("区服标签名称已存在")
        except Exception as e:
            raise Exception(f"添加失败: {str(e)}")
        finally:
            conn.close()

    def delete_tag(self, id):
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("DELETE FROM 区服标签 WHERE id=?", (id,))
            conn.commit()
            return cursor.rowcount > 0
        except Exception as e:
            raise Exception(f"删除失败: {str(e)}")
        finally:
            conn.close()

    def get_all_tags(self):
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT id, 标签名称 FROM 区服标签 ORDER BY id")
            return cursor.fetchall()
        except Exception as e:
            raise Exception(f"查询失败: {str(e)}")
        finally:
            conn.close()

class ServerConfigPage(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.db_path = "DB/account.db"
        self.tag_manager = ServerTagManager(self.db_path)
        self.setup_ui()
        self.load_data()

    def setup_ui(self):
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        
        # 操作按钮
        btn_layout = QHBoxLayout()
        btn_layout.setContentsMargins(5, 5, 5, 5)
        
        self.add_btn = QPushButton("添加")
        self.add_btn.setStyleSheet("""
            QPushButton {
                background-color: rgba(37, 117, 252, 0.3);
                color: #00F5FF;
                border: 1px solid #00F5FF;
                border-radius: 4px;
                padding: 5px 10px;
            }
            QPushButton:hover {
                background-color: rgba(0, 245, 255, 0.5);
                color: white;
            }
        """)
        self.add_btn.clicked.connect(self.add_tag)
        
        self.delete_btn = QPushButton("删除")
        self.delete_btn.setStyleSheet(self.add_btn.styleSheet())
        self.delete_btn.clicked.connect(self.delete_tag)
        
        btn_layout.addWidget(self.add_btn)
        btn_layout.addWidget(self.delete_btn)
        btn_layout.addStretch()
        
        # 标签表格
        self.table = QTableWidget()
        self.table.setColumnCount(2)
        self.table.setHorizontalHeaderLabels(["ID", "区服标签名称"])
        self.table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.table.verticalHeader().setVisible(False)
        self.table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        
        # 表格样式
        self.table.setStyleSheet("""
            QTableWidget {
                background-color: rgba(30, 35, 60, 0.5);
                border: 1px solid rgba(0, 245, 255, 0.2);
                color: #C0C0C0;
                gridline-color: rgba(0, 245, 255, 0.1);
            }
            QHeaderView::section {
                background-color: transparent;
                color: #00F5FF;
                padding-left: 8px;
                padding-right: 8px;
                border: none;
            }
        """)
        
        main_layout.addWidget(self.table)
        main_layout.addLayout(btn_layout)

    def load_data(self):
        try:
            tags = self.tag_manager.get_all_tags()
            self.table.setRowCount(len(tags))
            
            for row, tag in enumerate(tags):
                item_id = QTableWidgetItem(str(tag[0]))
                item_id.setTextAlignment(Qt.AlignCenter)
                self.table.setItem(row, 0, item_id)
                
                item_name = QTableWidgetItem(tag[1])
                item_name.setTextAlignment(Qt.AlignCenter)
                self.table.setItem(row, 1, item_name)
                
                self.table.setRowHeight(row, 30)
                
        except Exception as e:
                print(f"加载区服标签失败: {str(e)}")
                self.table.setRowCount(0)

    def add_tag(self):
        dialog = QInputDialog(self)
        dialog.setWindowTitle("添加区服标签")
        dialog.setLabelText("请输入区服标签名称:")
        # 设置弹窗在主窗口中心显示
        dialog.setWindowFlags(Qt.Dialog | Qt.CustomizeWindowHint | Qt.WindowTitleHint)
        parent_window = self.parent()
        while parent_window and not isinstance(parent_window, QMainWindow):
            parent_window = parent_window.parent()
        if parent_window:
            parent_window.center_dialog(dialog)
        dialog.setStyleSheet("""
            QInputDialog {
                background-color: #1E233C;
                color: #FFFFFF;
                font-size: 14px;
            }
            QLabel {
                color: #00F5FF;
                font-size: 14px;
            }
            QLineEdit {
                background-color: rgba(30, 35, 60, 0.7);
                color: #FFFFFF;
                border: 1px solid #00F5FF;
                font-size: 14px;
                padding: 5px;
            }
            QPushButton {
                background-color: rgba(37, 117, 252, 0.3);
                color: #00F5FF;
                border: 1px solid #00F5FF;
                border-radius: 4px;
                padding: 5px 10px;
                min-width: 80px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: rgba(0, 245, 255, 0.5);
                color: white;
            }
        """)
        ok = dialog.exec_()
        name = dialog.textValue()
        if ok and name:
            try:
                self.tag_manager.add_tag(name)
                self.load_data()
            except Exception as e:
                msg = QMessageBox(self)
                msg.setIcon(QMessageBox.Critical)
                msg.setWindowTitle("错误提示")
                msg.setText(str(e))
                # 设置弹窗在主窗口中心显示
                parent_window = self.parent()
                while parent_window and not isinstance(parent_window, QMainWindow):
                    parent_window = parent_window.parent()
                if parent_window:
                    parent_window.center_dialog(msg)
                msg.setStyleSheet("""
                    QMessageBox {
                        background-color: #2A0A0A;
                        color: #FFFFFF;
                        font-size: 16px;
                        border: 2px solid #FF0000;
                    }
                    QLabel {
                        color: #FF7777;
                        font-size: 16px;
                        font-weight: bold;
                    }
                    QPushButton {
                        background-color: rgba(255, 50, 50, 0.7);
                        color: #FFFFFF;
                        border: 1px solid #FF0000;
                        border-radius: 4px;
                        padding: 8px 15px;
                        min-width: 100px;
                        font-size: 14px;
                        font-weight: bold;
                    }
                    QPushButton:hover {
                        background-color: rgba(255, 80, 80, 0.9);
                    }
                """)
                msg.exec_()

    def delete_tag(self):
        selected = self.table.currentRow()
        if selected == -1:
            msg = QMessageBox(self)
            msg.setIcon(QMessageBox.Warning)
            msg.setWindowTitle("提示")
            msg.setText("请先选择要删除的行")
            # 设置弹窗在主窗口中心显示
            parent_window = self.parent()
            while parent_window and not isinstance(parent_window, QMainWindow):
                parent_window = parent_window.parent()
            if parent_window:
                parent_window.center_dialog(msg)
            msg.setStyleSheet("""
                QMessageBox {
                    background-color: #1E233C;
                    color: #FFFFFF;
                    font-size: 14px;
                }
                QLabel {
                    color: #00F5FF;
                    font-size: 14px;
                }
                QPushButton {
                    background-color: rgba(37, 117, 252, 0.3);
                    color: #00F5FF;
                    border: 1px solid #00F5FF;
                    border-radius: 4px;
                    padding: 5px 10px;
                    min-width: 80px;
                    font-size: 14px;
                }
                QPushButton:hover {
                    background-color: rgba(0, 245, 255, 0.5);
                    color: white;
                }
            """)
            msg.exec_()
            return
            
        tag_id = int(self.table.item(selected, 0).text())
        tag_name = self.table.item(selected, 1).text()
        
        msg = QMessageBox(self)
        msg.setIcon(QMessageBox.Question)
        msg.setWindowTitle("确认删除")
        msg.setText(f"确定要删除区服标签 '{tag_name}' 吗?")
        # 设置弹窗在主窗口中心显示
        parent_window = self.parent()
        while parent_window and not isinstance(parent_window, QMainWindow):
            parent_window = parent_window.parent()
        if parent_window:
            parent_window.center_dialog(msg)
        msg.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
        msg.setStyleSheet("""
            QMessageBox {
                background-color: #1E233C;
                color: #FFFFFF;
                font-size: 14px;
            }
            QLabel {
                color: #00F5FF;
                font-size: 14px;
            }
            QPushButton {
                background-color: rgba(37, 117, 252, 0.3);
                color: #00F5FF;
                border: 1px solid #00F5FF;
                border-radius: 4px;
                padding: 5px 10px;
                min-width: 80px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: rgba(0, 245, 255, 0.5);
                color: white;
            }
        """)
        reply = msg.exec_()
        
        if reply == QMessageBox.Yes:
            try:
                if self.tag_manager.delete_tag(tag_id):
                    self.load_data()
            except Exception as e:
                msg = QMessageBox()
                msg.setIcon(QMessageBox.Critical)
                msg.setWindowTitle("错误提示")
                msg.setText(str(e))
                msg.setStyleSheet("""
                    QMessageBox {
                        background-color: #2A0A0A;
                        color: #FFFFFF;
                        font-size: 16px;
                        border: 2px solid #FF0000;
                    }
                    QLabel {
                        color: #FF7777;
                        font-size: 16px;
                        font-weight: bold;
                    }
                    QPushButton {
                        background-color: rgba(255, 50, 50, 0.7);
                        color: #FFFFFF;
                        border: 1px solid #FF0000;
                        border-radius: 4px;
                        padding: 8px 15px;
                        min-width: 100px;
                        font-size: 14px;
                        font-weight: bold;
                    }
                    QPushButton:hover {
                        background-color: rgba(255, 80, 80, 0.9);
                    }
                """)
                msg.exec_()
