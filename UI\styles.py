"""
UI样式和常量定义模块
包含所有UI组件的样式定义、字体配置等
"""
from PyQt5.QtGui import QFont

class UIStyles:
    """UI样式常量类"""
    
    # 四级字体系统
    TITLE_FONT = QFont("Segoe UI Semibold", 12)
    SUBTITLE_FONT = QFont("Segoe UI", 10)
    BODY_FONT = QFont("Segoe UI", 9)
    CODE_FONT = QFont("Consolas", 9)
    
    # 主容器样式
    MAIN_CONTAINER_STYLE = """
        #MainContainer {
            background-color: rgba(20, 25, 45, 0.7);
            border-radius: 12px;
            border: 1px solid rgba(0, 245, 255, 0.3);
        }
    """
    
    # 表格样式
    PHONE_TABLE_STYLE = """
        QTableWidget {
            background-color: rgba(30, 35, 60, 0.5);
            border: 1px solid rgba(0, 245, 255, 0.2);
            color: #C0C0C0;
            font-family: "Segoe UI";
            font-size: 9pt;
            gridline-color: rgba(0, 245, 255, 0.1);
        }
        QTableWidget QTableCornerButton::section,
        QTableWidget::viewport {
            background-color: rgba(30, 35, 60, 0.5);
            border: none;
        }
        QHeaderView::section {
            background-color: rgba(106, 17, 203, 0.3);
            color: #00F5FF;
            padding-left: 8px;
            padding-right: 8px;
            border: none;
            font-family: "Segoe UI";
            font-size: 10pt;
            font-weight: normal;
        }
        QTableWidget::item {
            padding: 5px;
            text-align: center;
            vertical-align: middle;
        }
        QTableWidget::item:selected {
            background-color: rgba(37, 117, 252, 0.5);
            color: #C0C0C0;
        }
    """
    
    # 导航按钮样式
    NAV_BUTTON_STYLE = """
        QPushButton {
            color: #C0C0C0;
            background-color: transparent;
            text-align: left;
            padding-left: 10px;
            border-radius: 4px;
            font-size: 12px;
        }
        QPushButton:hover {
            color: #00F5FF;
            background-color: rgba(106, 17, 203, 0.3);
        }
        QPushButton:pressed {
            background-color: rgba(37, 117, 252, 0.5);
        }
        QPushButton:checked {
            color: #00F5FF;
            background-color: rgba(37, 117, 252, 0.5);
            border-left: 3px solid #00F5FF;
        }
    """
    
    # 服务控制按钮样式
    START_BUTTON_STYLE = """
        QPushButton {
            color: #00FF00;
            background-color: rgba(37, 117, 252, 0.3);
            border: 1px solid #00FF00;
            border-radius: 4px;
            padding: 8px 12px;
            margin: 2px 5px;
            font-family: "Segoe UI";
            font-size: 9pt;
            min-width: 100px;
        }
        QPushButton:hover {
            background-color: rgba(0, 255, 0, 0.5);
        }
    """
    
    STOP_BUTTON_STYLE = """
        QPushButton {
            color: #FF5555;
            background-color: rgba(37, 117, 252, 0.3);
            border: 1px solid #FF5555;
            border-radius: 4px;
            padding: 8px 12px;
            margin: 2px 5px;
            font-family: "Segoe UI";
            font-size: 9pt;
            min-width: 100px;
        }
        QPushButton:hover {
            background-color: rgba(255, 85, 85, 0.5);
        }
    """
    
    # 窗口控制按钮样式
    CONTROL_BUTTON_STYLE = """
        QPushButton {
            color: #00F5FF;
            background-color: rgba(37, 117, 252, 0.3);
            border-radius: 15px;
            border: 1px solid rgba(0, 245, 255, 0.3);
            font-size: 16px;
        }
        QPushButton:hover {
            background-color: rgba(37, 117, 252, 0.5);
        }
        QPushButton:pressed {
            background-color: rgba(0, 245, 255, 0.7);
        }
    """
    
    # 状态标签样式
    STATUS_LABEL_STYLE = """
        QLabel {
            color: #FF5555;
            font-family: "Segoe UI";
            font-size: 11pt;
            font-weight: 600;
            letter-spacing: 1px;
            min-width: 80px;
            text-align: center;
            text-transform: uppercase;
        }
    """
    
    # 标题标签样式
    TITLE_LABEL_STYLE = """
        QLabel {
            color: #00F5FF;
            font-family: "Segoe UI Semibold";
            font-size: 14pt;
            font-weight: 600;
            letter-spacing: 1px;
            padding-bottom: 2px;
        }
    """
    
    # 输出文本框样式
    OUTPUT_TEXT_STYLE = """
        QTextEdit {
            background-color: rgba(20, 25, 45, 0.7);
            color: #E0E0E0;
            border: none;
            font-family: Consolas;
            font-size: 9pt;
            line-height: 1.4;
            padding: 10px;
        }
    """
    
    # 清空日志按钮样式
    CLEAR_OUTPUT_BUTTON_STYLE = """
        QPushButton {
            color: #FF5555;
            background-color: rgba(106, 17, 203, 0.3);
            border: 1px solid #FF5555;
            border-radius: 4px;
            padding: 4px 8px;
            font-size: 11px;
        }
        QPushButton:hover {
            background-color: rgba(255, 85, 85, 0.5);
        }
    """
    
    # 导航面板样式
    NAV_PANEL_STYLE = """
        background-color: rgba(20, 25, 45, 0.5);
        border-radius: 8px;
        border: 1px solid rgba(0, 245, 255, 0.2);
    """
    
    # 内容面板样式
    CONTENT_PANEL_STYLE = """
        background-color: rgba(20, 25, 45, 0.5);
        border-radius: 8px;
        border: 1px solid rgba(0, 245, 255, 0.2);
    """
    
    # 输出窗口框架样式
    OUTPUT_FRAME_STYLE = """
        background-color: rgba(30, 35, 60, 0.5);
        border-radius: 8px;
        border: 1px solid rgba(0, 245, 255, 0.2);
    """
    
    # 分割器样式
    SPLITTER_STYLE = """
        QSplitter::handle {
            background-color: rgba(0, 245, 255, 0.1);
            height: 2px;
        }
    """
    
    # 消息框样式
    MESSAGE_BOX_STYLE = """
        QMessageBox {
            background-color: rgba(20, 25, 45, 0.95);
            border: 2px solid #00F5FF;
            border-top: 3px solid qlineargradient(
                x1:0, y1:0, x2:0, y2:1,
                stop:0 #00F5FF, stop:1 transparent
            );
        }
        QLabel {
            color: #00F5FF;
            font-size: 14px;
            margin-top: 5px;
        }
        QPushButton {
            color: #00F5FF;
            background-color: rgba(106, 17, 203, 0.3);
            border: 1px solid #00F5FF;
            border-radius: 4px;
            padding: 5px 15px;
            min-width: 80px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: rgba(0, 245, 255, 0.5);
            color: white;
            border: 1px solid white;
        }
    """

class UIColors:
    """UI颜色常量类"""
    
    # 主要颜色
    PRIMARY_CYAN = "#00F5FF"
    PRIMARY_PURPLE = "rgba(106, 17, 203, 0.3)"
    PRIMARY_BLUE = "rgba(37, 117, 252, 0.3)"
    
    # 状态颜色
    SUCCESS_GREEN = "#00FF00"
    ERROR_RED = "#FF5555"
    WARNING_ORANGE = "#FFA500"
    INFO_CYAN = "#00F5FF"
    
    # 文本颜色
    TEXT_PRIMARY = "#C0C0C0"
    TEXT_SECONDARY = "#E0E0E0"
    TEXT_DISABLED = "#606060"
    
    # 背景颜色
    BG_MAIN = "rgba(20, 25, 45, 0.7)"
    BG_SECONDARY = "rgba(30, 35, 60, 0.5)"
    BG_TRANSPARENT = "transparent"
