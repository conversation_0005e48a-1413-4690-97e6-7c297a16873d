import sqlite3
from datetime import datetime
from PyQt5.QtCore import Qt
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, 
                            QFrame, QLabel, QMessageBox, QPushButton,
                            QTableWidget, QTableWidgetItem, QHeaderView)
from PyQt5.QtGui import QColor

class ServerManager:
    def __init__(self, db_path):
        self.db_path = db_path
        self.init_db()

    def init_db(self):
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS 区服表 (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    账号ID INTEGER NOT NULL,
                    区服名称 TEXT NOT NULL,
                    完成状态 TEXT DEFAULT '否',
                    完成日期 TEXT DEFAULT '',
                    FOREIGN KEY(账号ID) REFERENCES 账号表(id)
                )
            ''')
            conn.commit()
            conn.close()
        except Exception as e:
            raise Exception(f"初始化区服数据库失败: {str(e)}")

    def add_server(self, 账号ID, server_name, status="否", complete_date=""):
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO 区服表 (账号ID, 区服名称, 完成状态, 完成日期)
                VALUES (?, ?, ?, ?)
            ''', (账号ID, server_name, status, complete_date))
            conn.commit()
            conn.close()
            return True
        except Exception as e:
            raise Exception(f"添加区服失败: {str(e)}")

    def get_current_date(self):
        """获取当前日期，格式为YYYY-MM-DD"""
        return datetime.now().strftime("%Y-%m-%d")

    def update_server(self, server_id, server_name=None, status=None, complete_date=None):
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 强制处理日期
            if status == "是":
                if complete_date is None:
                    complete_date = self.get_current_date()
            elif status == "否":
                complete_date = ""  # 强制清空完成日期
                
            # 处理NULL值
            if complete_date is None:
                complete_date = ""
                
            # 构建更新语句和参数
            updates = []
            params = []
            
            if server_name is not None:
                updates.append("区服名称=?")
                params.append(server_name)
                
            if status is not None:
                updates.append("完成状态=?")
                params.append(status)
                
            updates.append("完成日期=?")
            params.append(complete_date)
                
            params.append(server_id)
            sql = f"UPDATE 区服表 SET {', '.join(updates)} WHERE id=?"
            
            print(f"执行SQL: {sql} 参数: {params}")  # 调试日志
            cursor.execute(sql, params)
                
            conn.commit()
            print("事务已提交")  # 调试日志
            conn.close()
            return True
        except Exception as e:
            raise Exception(f"更新区服失败: {str(e)}")

    def delete_server(self, server_id):
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("DELETE FROM 区服表 WHERE id=?", (server_id,))
            conn.commit()
            conn.close()
            return True
        except Exception as e:
            raise Exception(f"删除区服失败: {str(e)}")

    def get_servers_by_account(self, 账号ID):
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT id, 账号ID, 区服名称, 完成状态, 完成日期 FROM 区服表 WHERE 账号ID=?", (账号ID,))
            rows = cursor.fetchall()
            conn.close()
            return rows
        except Exception as e:
            raise Exception(f"获取区服列表失败: {str(e)}")

    def get_server_status_color(self, status):
        status_colors = {
            "是": QColor(0, 200, 0),  # 绿色表示完成
            "否": QColor(200, 0, 0)   # 红色表示未完成
        }
        # 兼容旧数据
        if status == "已完成":
            return QColor(0, 200, 0)
        elif status == "进行中" or status == "未完成":
            return QColor(200, 0, 0)
        return status_colors.get(status, QColor(200, 200, 200))

class ServerUI(QWidget):
    def __init__(self, servers, server_manager, parent=None):
        super().__init__(parent)
        self.servers = servers or []
        self.server_manager = server_manager
        self.parent_ui = parent
        from .角色 import CharacterUI
        self.character_ui = CharacterUI(self)
        self.setup_ui()

    def update_data(self, servers):
        self.servers = servers or []
        self.load_server_data()

    def update_servers(self, servers):
        """更新服务器数据并刷新UI"""
        self.servers = servers or []
        self.load_server_data()

    def setup_ui(self):
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(10)
        
        # 左侧区服表格
        server_frame = QFrame()
        server_frame.setFrameShape(QFrame.StyledPanel)
        server_layout = QVBoxLayout(server_frame)
        server_layout.setContentsMargins(0, 0, 0, 0)
        
        # 区服表格直接添加，不显示操作按钮
        self.server_table = QTableWidget()
        
        # 区服表格
        self.server_table = QTableWidget()
        self.server_table.setColumnCount(3)
        self.server_table.setHorizontalHeaderLabels(["区服名称", "完成状态", "完成日期"])
        self.server_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.server_table.verticalHeader().setVisible(False)
        self.server_table.setEditTriggers(QTableWidget.DoubleClicked)
        self.server_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.server_table.cellDoubleClicked.connect(self.on_cell_double_clicked)
        
        # 表格样式
        self.server_table.setStyleSheet("""
            QTableWidget {
                background-color: rgba(30, 35, 60, 0.5);
                border: 1px solid rgba(0, 245, 255, 0.2);
                color: #C0C0C0;
                gridline-color: rgba(0, 245, 255, 0.1);
            }
            QHeaderView::section {
                background-color: transparent;
                color: #00F5FF;
                padding-left: 8px;
                padding-right: 8px;
                border: none;
                font-size: 10pt;
            }
        """)
        
        # 连接区服选择信号
        self.server_table.itemSelectionChanged.connect(self.on_server_selected)
        
        server_layout.addWidget(self.server_table)
        main_layout.addWidget(server_frame, 1)
        
        # 右侧角色表格
        char_frame = QFrame()
        char_frame.setFrameShape(QFrame.StyledPanel)
        char_layout = QVBoxLayout(char_frame)
        char_layout.setContentsMargins(0, 0, 0, 0)
        
        # 直接添加角色表格，不显示操作按钮
        
        # 使用CharacterUI替代原有角色表格
        char_layout.addWidget(self.character_ui.char_table)
        main_layout.addWidget(char_frame, 1)
        
        # 初始化时显示无角色数据提示
        self.character_ui.show_no_character_data()
        
        # 填充数据
        self.load_server_data()

    def load_server_data(self):
        self.server_table.setRowCount(0)
        
        if not self.servers:
            self.server_table.setRowCount(1)
            empty_item = QTableWidgetItem("暂无区服数据")
            empty_item.setTextAlignment(Qt.AlignCenter)
            empty_item.setFlags(Qt.ItemIsEnabled)
            empty_item.setForeground(QColor(150, 150, 150))
            self.server_table.setItem(0, 0, empty_item)
            self.server_table.setSpan(0, 0, 1, 3)
            return
            
        for row, server in enumerate(self.servers):
            self.server_table.insertRow(row)
            
            # 区服名称
            name_item = QTableWidgetItem(server[2])
            name_item.setFlags(Qt.ItemIsEnabled | Qt.ItemIsSelectable)
            name_item.setTextAlignment(Qt.AlignCenter)
            self.server_table.setItem(row, 0, name_item)
            
            # 状态 - 转换为"是/否"显示
            status = server[3]
            if status == "已完成":
                status = "是"
            elif status in ["进行中", "未完成"]:
                status = "否"
                
            status_item = QTableWidgetItem(status)
            status_item.setFlags(Qt.ItemIsEnabled | Qt.ItemIsSelectable)
            status_item.setTextAlignment(Qt.AlignCenter)
            status_item.setForeground(
                self.server_manager.get_server_status_color(status))
            self.server_table.setItem(row, 1, status_item)
            
            # 完成日期
            complete_date = server[4] if len(server) > 4 else ""
            date_item = QTableWidgetItem(complete_date)
            date_item.setFlags(Qt.ItemIsEnabled | Qt.ItemIsSelectable)
            date_item.setTextAlignment(Qt.AlignCenter)
            self.server_table.setItem(row, 2, date_item)
            
            self.server_table.setRowHeight(row, 30)

    def load_character_data(self, server_id):
        print(f"加载角色数据，server_id={server_id}")  # 调试输出
        self.character_ui.load_character_data([])  # 传入空列表显示无数据
        return
        
        # TODO: 从数据库加载角色数据
        # characters = []  # 初始化为空列表
        # if characters:
        #     self.character_ui.load_character_data(characters)

    def on_cell_double_clicked(self, row, column):
        """处理单元格双击事件"""
        if column != 1:  # 只允许编辑"完成状态"列
            return
            
        item = self.server_table.item(row, column)
        current_status = item.text()
        new_status = "是" if current_status == "否" else "否"
        
        try:
            server_id = self.servers[row][0]
            # 强制清空完成日期当状态为"否"时
            if self.server_manager.update_server(server_id, status=new_status):
                item.setText(new_status)
                item.setForeground(
                    self.server_manager.get_server_status_color(new_status))
                
                # 更新日期显示
                date_item = self.server_table.item(row, 2)
                if date_item:
                    if new_status == "是":
                        date_item.setText(self.server_manager.get_current_date())
                    else:
                        date_item.setText("")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"更新状态失败: {str(e)}")

    def on_server_selected(self):
        selected = self.server_table.currentRow()
        if selected >= 0 and selected < len(self.servers):
            server_id = self.servers[selected][0]
            self.load_character_data(server_id)

    def add_server(self):
        # TODO: 实现添加区服逻辑
        pass

    def edit_server(self, server):
        # TODO: 实现编辑区服逻辑
        pass

    def delete_server(self, server):
        """删除区服"""
        confirm = QMessageBox(self)
        confirm.setWindowTitle("确认删除")
        confirm.setText(f"确定要删除区服 {server[2]} 吗?")
        confirm.setIcon(QMessageBox.Question)
        confirm.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
        confirm.setAttribute(Qt.WA_DeleteOnClose)
        confirm.setStyleSheet("""
            QMessageBox {
                background-color: rgba(20, 25, 45, 0.95);
                border: 2px solid #FF5555;
                border-top: 3px solid qlineargradient(
                    x1:0, y1:0, x2:0, y2:1,
                    stop:0 #FF5555, stop:1 transparent
                );
            }
            QLabel {
                color: #FFFFFF;
                font-size: 14px;
                margin-top: 5px;
                font-weight: bold;
            }
            QPushButton {
                color: #FFFFFF;
                background-color: rgba(255, 85, 85, 0.5);
                border: 1px solid #FF5555;
                border-radius: 4px;
                padding: 5px 15px;
                min-width: 80px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: rgba(255, 85, 85, 0.7);
                border: 1px solid white;
            }
        """)
        
        if confirm.exec_() == QMessageBox.Yes:
            try:
                if self.server_manager.delete_server(server[0]):
                    # 从列表中移除已删除的区服
                    self.servers = [s for s in self.servers if s[0] != server[0]]
                    self.load_server_data()
            except Exception as e:
                QMessageBox.critical(self, "错误", f"删除区服失败: {str(e)}")

    def add_character(self):
        """添加角色方法"""
        # TODO: 实现添加角色逻辑
        print("添加角色按钮被点击")
