"""
导航面板组件模块
包含左侧导航按钮和系统监控组件
"""
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtWidgets import QWidget, QVBoxLayout, QFrame, QPushButton
from .styles import UIStyles
try:
    from .系统监控 import SystemMonitor
except ImportError:
    from UI.系统监控 import SystemMonitor

class NavigationPanel(QWidget):
    """导航面板组件"""
    
    # 定义页面切换信号
    page_changed = pyqtSignal(str, object)  # 页面名称, 按钮对象
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.nav_buttons = []
        self.init_ui()
        
    def init_ui(self):
        """初始化导航面板UI"""
        # 创建导航框架
        nav_frame = QFrame()
        nav_frame.setFixedWidth(130)
        nav_frame.setStyleSheet(UIStyles.NAV_PANEL_STYLE)
        
        # 创建主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        nav_layout = QVBoxLayout(nav_frame)
        nav_layout.setContentsMargins(0, 0, 0, 0)
        nav_layout.setSpacing(5)
        
        # 添加导航按钮
        self._create_navigation_buttons(nav_layout)
        
        # 添加Stretch确保监控区域固定在底部
        nav_layout.addStretch()
        
        # 添加系统监控组件
        self.system_monitor = SystemMonitor()
        nav_layout.addWidget(self.system_monitor)
        
        main_layout.addWidget(nav_frame)
        
    def _create_navigation_buttons(self, layout):
        """创建导航按钮"""
        nav_buttons_config = [
            ("连接管理", "📶"),
            ("账号管理", "👤"),
            ("脚本配置", "📜"),
            ("系统设置", "⚙️")
        ]
        
        for text, icon in nav_buttons_config:
            btn = self.create_nav_button(text, icon)
            self.nav_buttons.append(btn)
            layout.addWidget(btn)
            
    def create_nav_button(self, text, icon):
        """创建导航按钮"""
        btn = QPushButton(f"  {icon}   {text}")  # 增加图标和文本之间的间距
        btn.setFixedHeight(45)
        btn.setCheckable(True)
        btn.setStyleSheet(UIStyles.NAV_BUTTON_STYLE)
        
        # 绑定按钮点击事件
        btn.clicked.connect(lambda: self.switch_page(text, btn))
            
        return btn
        
    def switch_page(self, page_name, button=None):
        """切换内容页面"""
        try:
            print(f"切换到页面: {page_name}")  # 控制台输出当前页面名称
            
            # 更新按钮选中状态
            if button and hasattr(self, 'nav_buttons'):
                # 取消所有按钮的选中状态
                for btn in self.nav_buttons:
                    btn.setChecked(False)
                # 设置当前按钮为选中状态
                button.setChecked(True)
            
            # 发射页面切换信号
            self.page_changed.emit(page_name, button)
            
        except Exception as e:
            print(f"切换页面时发生错误: {str(e)}")
            
    def set_current_page(self, page_name):
        """设置当前页面（外部调用）"""
        for btn in self.nav_buttons:
            btn_text = btn.text().split()[-1]  # 获取按钮文本（去掉图标）
            if btn_text == page_name:
                btn.setChecked(True)
                self.switch_page(page_name, btn)
                break
            else:
                btn.setChecked(False)
